{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Cred<PERSON><PERSON><PERSON>rovider from 'next-auth/providers/credentials'\nimport { PrismaAdapter } from '@auth/prisma-adapter'\nimport { prisma } from './prisma'\nimport bcrypt from 'bcryptjs'\nimport { UserRole } from '@prisma/client'\n\n// Cache for role permissions to avoid database calls on every request\nlet rolePermissionsCache: Record<string, string[]> = {}\nlet cacheLastUpdated = 0\nconst CACHE_DURATION = 5 * 60 * 1000 // 5 minutes\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma) as any,\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user || !user.isActive) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        // Update last login\n        await prisma.user.update({\n          where: { id: user.id },\n          data: { lastLogin: new Date() }\n        })\n\n        return {\n          id: user.id,\n          email: user.email,\n          firstName: user.firstName,\n          lastName: user.lastName,\n          role: user.role,\n          avatar: user.avatar || undefined,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n        token.firstName = user.firstName\n        token.lastName = user.lastName\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as UserRole\n        session.user.firstName = token.firstName as string\n        session.user.lastName = token.lastName as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n    error: '/auth/error',\n  }\n}\n\n// Load role permissions from database with caching\nasync function loadRolePermissions(): Promise<Record<string, string[]>> {\n  const now = Date.now()\n\n  // Return cached permissions if still valid\n  if (cacheLastUpdated && (now - cacheLastUpdated) < CACHE_DURATION) {\n    return rolePermissionsCache\n  }\n\n  try {\n    // Fetch all active role permissions from database\n    const permissions = await prisma.rolePermission.findMany({\n      where: { isActive: true },\n      select: { role: true, permission: true }\n    })\n\n    // Group permissions by role\n    const rolePermissions: Record<string, string[]> = {}\n    for (const perm of permissions) {\n      if (!rolePermissions[perm.role]) {\n        rolePermissions[perm.role] = []\n      }\n      rolePermissions[perm.role].push(perm.permission)\n    }\n\n    // Update cache\n    rolePermissionsCache = rolePermissions\n    cacheLastUpdated = now\n\n    return rolePermissions\n  } catch (error) {\n    console.error('Error loading role permissions from database:', error)\n    // Return empty permissions on error to be safe\n    return {}\n  }\n}\n\n// Get role permissions (cached)\nexport async function getRolePermissions(): Promise<Record<string, string[]>> {\n  return await loadRolePermissions()\n}\n\n// Clear permissions cache (call this when permissions are updated)\nexport function clearPermissionsCache() {\n  rolePermissionsCache = {}\n  cacheLastUpdated = 0\n}\n\n// Initialize permissions cache on server startup\nexport async function initializePermissionsCache() {\n  try {\n    await loadRolePermissions()\n    console.log('✅ Permissions cache initialized')\n  } catch (error) {\n    console.error('❌ Failed to initialize permissions cache:', error)\n  }\n}\n\nexport async function hasPermission(userRole: UserRole, permission: string): Promise<boolean> {\n  const rolePermissions = await getRolePermissions()\n  return rolePermissions[userRole]?.includes(permission) || false\n}\n\nexport async function checkPermissions(userRole: UserRole, requiredPermissions: string[]): Promise<boolean> {\n  const rolePermissions = await getRolePermissions()\n  return requiredPermissions.every(permission => rolePermissions[userRole]?.includes(permission) || false)\n}\n\n// Synchronous version for backward compatibility (uses cache)\nexport function hasPermissionSync(userRole: UserRole, permission: string): boolean {\n  return rolePermissionsCache[userRole]?.includes(permission) || false\n}\n\nexport function checkPermissionsSync(userRole: UserRole, requiredPermissions: string[]): boolean {\n  return requiredPermissions.every(permission => hasPermissionSync(userRole, permission))\n}\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AACA;AACA;AACA;;;;;AAGA,sEAAsE;AACtE,IAAI,uBAAiD,CAAC;AACtD,IAAI,mBAAmB;AACvB,MAAM,iBAAiB,IAAI,KAAK,KAAK,YAAY;;AAE1C,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;oBAC3B,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,oBAAoB;gBACpB,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;oBACvB,OAAO;wBAAE,IAAI,KAAK,EAAE;oBAAC;oBACrB,MAAM;wBAAE,WAAW,IAAI;oBAAO;gBAChC;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,WAAW,KAAK,SAAS;oBACzB,UAAU,KAAK,QAAQ;oBACvB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM,IAAI;gBACzB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;AACF;AAEA,mDAAmD;AACnD,eAAe;IACb,MAAM,MAAM,KAAK,GAAG;IAEpB,2CAA2C;IAC3C,IAAI,oBAAoB,AAAC,MAAM,mBAAoB,gBAAgB;QACjE,OAAO;IACT;IAEA,IAAI;QACF,kDAAkD;QAClD,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;YACvD,OAAO;gBAAE,UAAU;YAAK;YACxB,QAAQ;gBAAE,MAAM;gBAAM,YAAY;YAAK;QACzC;QAEA,4BAA4B;QAC5B,MAAM,kBAA4C,CAAC;QACnD,KAAK,MAAM,QAAQ,YAAa;YAC9B,IAAI,CAAC,eAAe,CAAC,KAAK,IAAI,CAAC,EAAE;gBAC/B,eAAe,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE;YACjC;YACA,eAAe,CAAC,KAAK,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,UAAU;QACjD;QAEA,eAAe;QACf,uBAAuB;QACvB,mBAAmB;QAEnB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iDAAiD;QAC/D,+CAA+C;QAC/C,OAAO,CAAC;IACV;AACF;AAGO,eAAe;IACpB,OAAO,MAAM;AACf;AAGO,SAAS;IACd,uBAAuB,CAAC;IACxB,mBAAmB;AACrB;AAGO,eAAe;IACpB,IAAI;QACF,MAAM;QACN,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;IAC7D;AACF;AAEO,eAAe,cAAc,QAAkB,EAAE,UAAkB;IACxE,MAAM,kBAAkB,MAAM;IAC9B,OAAO,eAAe,CAAC,SAAS,EAAE,SAAS,eAAe;AAC5D;AAEO,eAAe,iBAAiB,QAAkB,EAAE,mBAA6B;IACtF,MAAM,kBAAkB,MAAM;IAC9B,OAAO,oBAAoB,KAAK,CAAC,CAAA,aAAc,eAAe,CAAC,SAAS,EAAE,SAAS,eAAe;AACpG;AAGO,SAAS,kBAAkB,QAAkB,EAAE,UAAkB;IACtE,OAAO,oBAAoB,CAAC,SAAS,EAAE,SAAS,eAAe;AACjE;AAEO,SAAS,qBAAqB,QAAkB,EAAE,mBAA6B;IACpF,OAAO,oBAAoB,KAAK,CAAC,CAAA,aAAc,kBAAkB,UAAU;AAC7E", "debugId": null}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/app/api/admin/backups/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions, hasPermission } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\nimport { z } from 'zod'\nimport { exec } from 'child_process'\nimport { promisify } from 'util'\nimport path from 'path'\nimport fs from 'fs/promises'\nimport { existsSync } from 'fs'\n\nconst execAsync = promisify(exec)\n\nconst createBackupSchema = z.object({\n  description: z.string().optional(),\n  backupType: z.enum(['MANUAL', 'AUTOMATIC']).default('MANUAL')\n})\n\n// GET /api/admin/backups - List all backups\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {\n      return NextResponse.json({ error: 'Unauthorized - Super Admin access required' }, { status: 401 })\n    }\n\n    const backups = await prisma.databaseBackup.findMany({\n      include: {\n        user: {\n          select: {\n            firstName: true,\n            lastName: true,\n            email: true\n          }\n        }\n      },\n      orderBy: { createdAt: 'desc' }\n    })\n\n    // Convert BigInt to string for JSON serialization\n    const serializedBackups = backups.map(backup => ({\n      ...backup,\n      fileSize: backup.fileSize.toString()\n    }))\n\n    return NextResponse.json(serializedBackups)\n  } catch (error) {\n    console.error('Error fetching backups:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n\n// POST /api/admin/backups - Create a new backup\nexport async function POST(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session?.user || session.user.role !== 'SUPER_ADMIN') {\n      return NextResponse.json({ error: 'Unauthorized - Super Admin access required' }, { status: 401 })\n    }\n\n    const body = await request.json()\n    const validatedData = createBackupSchema.parse(body)\n\n    // Generate unique filename\n    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')\n    const filename = `backup_${timestamp}.sql`\n    const backupDir = path.join(process.cwd(), 'backups')\n    const filePath = path.join(backupDir, filename)\n\n    // Ensure backup directory exists\n    try {\n      await fs.mkdir(backupDir, { recursive: true })\n    } catch (error) {\n      console.error('Error creating backup directory:', error)\n    }\n\n    // Create backup record in database\n    const backup = await prisma.databaseBackup.create({\n      data: {\n        filename,\n        originalName: filename,\n        fileSize: 0,\n        backupType: validatedData.backupType,\n        status: 'CREATING',\n        filePath,\n        createdBy: session.user.id,\n        description: validatedData.description\n      },\n      include: {\n        user: {\n          select: {\n            firstName: true,\n            lastName: true,\n            email: true\n          }\n        }\n      }\n    })\n\n    // Start backup process asynchronously\n    createDatabaseBackup(backup.id, filePath)\n      .catch(error => {\n        console.error('Backup creation failed:', error)\n        // Update backup status to failed\n        prisma.databaseBackup.update({\n          where: { id: backup.id },\n          data: {\n            status: 'FAILED',\n            errorMessage: error.message,\n            completedAt: new Date()\n          }\n        }).catch(console.error)\n      })\n\n    // Convert BigInt to string for JSON serialization\n    const serializedBackup = {\n      ...backup,\n      fileSize: backup.fileSize.toString()\n    }\n\n    return NextResponse.json(serializedBackup, { status: 201 })\n  } catch (error) {\n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { error: 'Validation error', details: error.errors },\n        { status: 400 }\n      )\n    }\n\n    console.error('Error creating backup:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n\n// Function to create database backup\nasync function createDatabaseBackup(backupId: string, filePath: string) {\n  try {\n    const dbUrl = process.env.DATABASE_URL\n    if (!dbUrl) {\n      throw new Error('DATABASE_URL not found')\n    }\n\n    // Parse database URL\n    const url = new URL(dbUrl)\n    const host = url.hostname\n    const port = url.port || '5432'\n    const database = url.pathname.slice(1)\n    const username = url.username\n    const password = url.password\n\n    // Set environment variables for pg_dump\n    const env = {\n      ...process.env,\n      PGPASSWORD: password\n    }\n\n    // Create pg_dump command\n    const command = `pg_dump -h ${host} -p ${port} -U ${username} -d ${database} --no-password --verbose --clean --if-exists --create > \"${filePath}\"`\n\n    console.log('Starting database backup...')\n    await execAsync(command, { env })\n\n    // Get file size\n    const stats = await fs.stat(filePath)\n    const fileSize = stats.size\n\n    // Update backup record\n    await prisma.databaseBackup.update({\n      where: { id: backupId },\n      data: {\n        status: 'COMPLETED',\n        fileSize: BigInt(fileSize),\n        completedAt: new Date()\n      }\n    })\n\n    console.log('Database backup completed successfully')\n  } catch (error) {\n    console.error('Database backup failed:', error)\n    throw error\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAGA,MAAM,YAAY,CAAA,GAAA,iGAAA,CAAA,YAAS,AAAD,EAAE,mHAAA,CAAA,OAAI;AAEhC,MAAM,qBAAqB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,aAAa,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,YAAY,+KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAU;KAAY,EAAE,OAAO,CAAC;AACtD;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,QAAQ,QAAQ,IAAI,CAAC,IAAI,KAAK,eAAe;YACzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA6C,GAAG;gBAAE,QAAQ;YAAI;QAClG;QAEA,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;YACnD,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,WAAW;wBACX,UAAU;wBACV,OAAO;oBACT;gBACF;YACF;YACA,SAAS;gBAAE,WAAW;YAAO;QAC/B;QAEA,kDAAkD;QAClD,MAAM,oBAAoB,QAAQ,GAAG,CAAC,CAAA,SAAU,CAAC;gBAC/C,GAAG,MAAM;gBACT,UAAU,OAAO,QAAQ,CAAC,QAAQ;YACpC,CAAC;QAED,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,SAAS,QAAQ,QAAQ,IAAI,CAAC,IAAI,KAAK,eAAe;YACzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA6C,GAAG;gBAAE,QAAQ;YAAI;QAClG;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,gBAAgB,mBAAmB,KAAK,CAAC;QAE/C,2BAA2B;QAC3B,MAAM,YAAY,IAAI,OAAO,WAAW,GAAG,OAAO,CAAC,SAAS;QAC5D,MAAM,WAAW,CAAC,OAAO,EAAE,UAAU,IAAI,CAAC;QAC1C,MAAM,YAAY,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;QAC3C,MAAM,WAAW,iGAAA,CAAA,UAAI,CAAC,IAAI,CAAC,WAAW;QAEtC,iCAAiC;QACjC,IAAI;YACF,MAAM,qHAAA,CAAA,UAAE,CAAC,KAAK,CAAC,WAAW;gBAAE,WAAW;YAAK;QAC9C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;QAEA,mCAAmC;QACnC,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,cAAc,CAAC,MAAM,CAAC;YAChD,MAAM;gBACJ;gBACA,cAAc;gBACd,UAAU;gBACV,YAAY,cAAc,UAAU;gBACpC,QAAQ;gBACR;gBACA,WAAW,QAAQ,IAAI,CAAC,EAAE;gBAC1B,aAAa,cAAc,WAAW;YACxC;YACA,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,WAAW;wBACX,UAAU;wBACV,OAAO;oBACT;gBACF;YACF;QACF;QAEA,sCAAsC;QACtC,qBAAqB,OAAO,EAAE,EAAE,UAC7B,KAAK,CAAC,CAAA;YACL,QAAQ,KAAK,CAAC,2BAA2B;YACzC,iCAAiC;YACjC,sHAAA,CAAA,SAAM,CAAC,cAAc,CAAC,MAAM,CAAC;gBAC3B,OAAO;oBAAE,IAAI,OAAO,EAAE;gBAAC;gBACvB,MAAM;oBACJ,QAAQ;oBACR,cAAc,MAAM,OAAO;oBAC3B,aAAa,IAAI;gBACnB;YACF,GAAG,KAAK,CAAC,QAAQ,KAAK;QACxB;QAEF,kDAAkD;QAClD,MAAM,mBAAmB;YACvB,GAAG,MAAM;YACT,UAAU,OAAO,QAAQ,CAAC,QAAQ;QACpC;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,kBAAkB;YAAE,QAAQ;QAAI;IAC3D,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,+KAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAoB,SAAS,MAAM,MAAM;YAAC,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,qCAAqC;AACrC,eAAe,qBAAqB,QAAgB,EAAE,QAAgB;IACpE,IAAI;QACF,MAAM,QAAQ,QAAQ,GAAG,CAAC,YAAY;QACtC,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,qBAAqB;QACrB,MAAM,MAAM,IAAI,IAAI;QACpB,MAAM,OAAO,IAAI,QAAQ;QACzB,MAAM,OAAO,IAAI,IAAI,IAAI;QACzB,MAAM,WAAW,IAAI,QAAQ,CAAC,KAAK,CAAC;QACpC,MAAM,WAAW,IAAI,QAAQ;QAC7B,MAAM,WAAW,IAAI,QAAQ;QAE7B,wCAAwC;QACxC,MAAM,MAAM;YACV,GAAG,QAAQ,GAAG;YACd,YAAY;QACd;QAEA,yBAAyB;QACzB,MAAM,UAAU,CAAC,WAAW,EAAE,KAAK,IAAI,EAAE,KAAK,IAAI,EAAE,SAAS,IAAI,EAAE,SAAS,yDAAyD,EAAE,SAAS,CAAC,CAAC;QAElJ,QAAQ,GAAG,CAAC;QACZ,MAAM,UAAU,SAAS;YAAE;QAAI;QAE/B,gBAAgB;QAChB,MAAM,QAAQ,MAAM,qHAAA,CAAA,UAAE,CAAC,IAAI,CAAC;QAC5B,MAAM,WAAW,MAAM,IAAI;QAE3B,uBAAuB;QACvB,MAAM,sHAAA,CAAA,SAAM,CAAC,cAAc,CAAC,MAAM,CAAC;YACjC,OAAO;gBAAE,IAAI;YAAS;YACtB,MAAM;gBACJ,QAAQ;gBACR,UAAU,OAAO;gBACjB,aAAa,IAAI;YACnB;QACF;QAEA,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;AACF", "debugId": null}}]}