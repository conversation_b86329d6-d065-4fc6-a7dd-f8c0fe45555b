{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 182, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,6JAAA,CAAA,aAAgB,MAG/B,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,6JAAA,CAAA,aAAgB,OAGlC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 257, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACb,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,KAGY;QAHZ,EACpB,SAAS,EACT,GAAG,OAC6B,GAHZ;IAIpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 403, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 438, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,KAAyD;QAAzD,EAAE,SAAS,EAAE,GAAG,OAAyC,GAAzD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,KAKgC;QALhC,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD,GALhC;IAMrB,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,KAIgC;QAJhC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C,GAJhC;IAKlB,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,SAAS,EACT,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 762, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/hooks/usePermissions.ts"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useCallback } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { UserRole } from '@prisma/client'\n\ninterface PermissionData {\n  allPermissions: Record<string, string[]>\n  currentPermissions: Record<string, string[]>\n  roles: string[]\n}\n\ninterface UsePermissionsReturn {\n  permissions: string[]\n  hasPermission: (permission: string) => boolean\n  hasAnyPermission: (permissions: string[]) => boolean\n  hasAllPermissions: (permissions: string[]) => boolean\n  loading: boolean\n  error: string | null\n  refetch: () => Promise<void>\n}\n\nexport function usePermissions(): UsePermissionsReturn {\n  const { data: session } = useSession()\n  const [permissionData, setPermissionData] = useState<PermissionData | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  const fetchPermissions = useCallback(async () => {\n    if (!session?.user?.role) {\n      setLoading(false)\n      return\n    }\n\n    try {\n      setLoading(true)\n      setError(null)\n\n      // Use user-specific permissions endpoint for non-admin users\n      const endpoint = session.user.role === 'SUPER_ADMIN'\n        ? '/api/admin/permissions'\n        : '/api/user/permissions'\n\n      const response = await fetch(endpoint)\n      if (!response.ok) {\n        throw new Error('Failed to fetch permissions')\n      }\n\n      const data = await response.json()\n\n      // Transform user permissions response to match admin format\n      if (endpoint === '/api/user/permissions') {\n        setPermissionData({\n          allPermissions: data.availablePermissions,\n          currentPermissions: { [data.role]: data.permissions },\n          roles: [data.role]\n        })\n      } else {\n        setPermissionData(data)\n      }\n    } catch (err) {\n      console.error('Error fetching permissions:', err)\n      setError(err instanceof Error ? err.message : 'Failed to fetch permissions')\n    } finally {\n      setLoading(false)\n    }\n  }, [session?.user?.role])\n\n  useEffect(() => {\n    fetchPermissions()\n  }, [fetchPermissions])\n\n  const userRole = session?.user?.role as UserRole\n  const userPermissions = permissionData?.currentPermissions?.[userRole] || []\n\n  const hasPermission = useCallback((permission: string): boolean => {\n    return userPermissions.includes(permission)\n  }, [userPermissions])\n\n  const hasAnyPermission = useCallback((permissions: string[]): boolean => {\n    return permissions.some(permission => userPermissions.includes(permission))\n  }, [userPermissions])\n\n  const hasAllPermissions = useCallback((permissions: string[]): boolean => {\n    return permissions.every(permission => userPermissions.includes(permission))\n  }, [userPermissions])\n\n  return {\n    permissions: userPermissions,\n    hasPermission,\n    hasAnyPermission,\n    hasAllPermissions,\n    loading,\n    error,\n    refetch: fetchPermissions\n  }\n}\n\n// Hook specifically for Credit Officer permissions\nexport function useCreditOfficerPermissions() {\n  const { data: session } = useSession()\n  const permissions = usePermissions()\n\n  // Only return permissions if user is a Credit Officer\n  if (session?.user?.role !== 'CREDIT_OFFICER') {\n    return {\n      ...permissions,\n      permissions: [],\n      hasPermission: () => false,\n      hasAnyPermission: () => false,\n      hasAllPermissions: () => false\n    }\n  }\n\n  return permissions\n}\n\n// Hook specifically for Higher Management permissions\nexport function useHigherManagementPermissions() {\n  const { data: session } = useSession()\n  const permissions = usePermissions()\n\n  // Only return permissions if user is Higher Management\n  if (session?.user?.role !== 'HIGHER_MANAGEMENT') {\n    return {\n      ...permissions,\n      permissions: [],\n      hasPermission: () => false,\n      hasAnyPermission: () => false,\n      hasAllPermissions: () => false\n    }\n  }\n\n  return permissions\n}\n\n// Permission constants for Credit Officer features\nexport const CREDIT_OFFICER_PERMISSIONS = {\n  // Customer management\n  CREATE_CUSTOMERS: 'customers:create',\n  READ_CUSTOMERS: 'customers:read',\n  UPDATE_CUSTOMERS: 'customers:update',\n\n  // Loan management\n  CREATE_LOANS: 'loans:create',\n  READ_LOANS: 'loans:read',\n  UPDATE_LOANS: 'loans:update',\n  DISBURSE_LOANS: 'loans:disburse',\n\n  // Payment management\n  CREATE_PAYMENTS: 'payments:create',\n  READ_PAYMENTS: 'payments:read',\n  UPDATE_PAYMENTS: 'payments:update',\n\n  // Document management\n  CREATE_DOCUMENTS: 'documents:create',\n  READ_DOCUMENTS: 'documents:read',\n  UPDATE_DOCUMENTS: 'documents:update',\n\n  // Reports\n  READ_REPORTS: 'reports:read',\n\n  // Loan types\n  READ_LOAN_TYPES: 'loan-types:read'\n} as const\n\n// Permission constants for Higher Management features\nexport const HIGHER_MANAGEMENT_PERMISSIONS = {\n  // Portfolio management\n  READ_PORTFOLIO: 'loans:read',\n  MANAGE_PORTFOLIO: 'loans:update',\n\n  // Financial oversight\n  READ_FINANCIAL_REPORTS: 'reports:read',\n  EXPORT_REPORTS: 'reports:export',\n  CREATE_REPORTS: 'reports:create',\n\n  // Risk management\n  READ_AUDIT: 'audit:read',\n  MANAGE_RISK: 'audit:update',\n\n  // Team oversight\n  READ_USERS: 'users:read',\n  MANAGE_USERS: 'users:update',\n\n  // Customer oversight\n  READ_CUSTOMERS: 'customers:read',\n  MANAGE_CUSTOMERS: 'customers:update',\n\n  // Payment oversight\n  READ_PAYMENTS: 'payments:read',\n  MANAGE_PAYMENTS: 'payments:update',\n\n  // Settings management\n  READ_SETTINGS: 'settings:read',\n  UPDATE_SETTINGS: 'settings:update'\n} as const\n\n// Helper function to check if user can access a specific dashboard feature\nexport function canAccessFeature(\n  permissions: string[],\n  requiredPermissions: string | string[]\n): boolean {\n  const required = Array.isArray(requiredPermissions) ? requiredPermissions : [requiredPermissions]\n  return required.every(permission => permissions.includes(permission))\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;;AAHA;;;AAsBO,SAAS;QA4CV,eAMa,gBACO;;IAlDxB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAC5E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE;gBAC9B;YAAL,IAAI,EAAC,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,IAAI,GAAE;gBACxB,WAAW;gBACX;YACF;YAEA,IAAI;gBACF,WAAW;gBACX,SAAS;gBAET,6DAA6D;gBAC7D,MAAM,WAAW,QAAQ,IAAI,CAAC,IAAI,KAAK,gBACnC,2BACA;gBAEJ,MAAM,WAAW,MAAM,MAAM;gBAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,4DAA4D;gBAC5D,IAAI,aAAa,yBAAyB;oBACxC,kBAAkB;wBAChB,gBAAgB,KAAK,oBAAoB;wBACzC,oBAAoB;4BAAE,CAAC,KAAK,IAAI,CAAC,EAAE,KAAK,WAAW;wBAAC;wBACpD,OAAO;4BAAC,KAAK,IAAI;yBAAC;oBACpB;gBACF,OAAO;oBACL,kBAAkB;gBACpB;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAChD,SAAU;gBACR,WAAW;YACb;QACF;uDAAG;QAAC,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,IAAI;KAAC;IAExB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF;mCAAG;QAAC;KAAiB;IAErB,MAAM,WAAW,oBAAA,+BAAA,iBAAA,QAAS,IAAI,cAAb,qCAAA,eAAe,IAAI;IACpC,MAAM,kBAAkB,CAAA,2BAAA,sCAAA,qCAAA,eAAgB,kBAAkB,cAAlC,yDAAA,kCAAoC,CAAC,SAAS,KAAI,EAAE;IAE5E,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE,CAAC;YACjC,OAAO,gBAAgB,QAAQ,CAAC;QAClC;oDAAG;QAAC;KAAgB;IAEpB,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;YACpC,OAAO,YAAY,IAAI;gEAAC,CAAA,aAAc,gBAAgB,QAAQ,CAAC;;QACjE;uDAAG;QAAC;KAAgB;IAEpB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE,CAAC;YACrC,OAAO,YAAY,KAAK;iEAAC,CAAA,aAAc,gBAAgB,QAAQ,CAAC;;QAClE;wDAAG;QAAC;KAAgB;IAEpB,OAAO;QACL,aAAa;QACb;QACA;QACA;QACA;QACA;QACA,SAAS;IACX;AACF;GA1EgB;;QACY,iJAAA,CAAA,aAAU;;;AA4E/B,SAAS;QAKV;;IAJJ,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,cAAc;IAEpB,sDAAsD;IACtD,IAAI,CAAA,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,IAAI,MAAK,kBAAkB;QAC5C,OAAO;YACL,GAAG,WAAW;YACd,aAAa,EAAE;YACf,eAAe,IAAM;YACrB,kBAAkB,IAAM;YACxB,mBAAmB,IAAM;QAC3B;IACF;IAEA,OAAO;AACT;IAhBgB;;QACY,iJAAA,CAAA,aAAU;QAChB;;;AAiBf,SAAS;QAKV;;IAJJ,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,cAAc;IAEpB,uDAAuD;IACvD,IAAI,CAAA,oBAAA,+BAAA,gBAAA,QAAS,IAAI,cAAb,oCAAA,cAAe,IAAI,MAAK,qBAAqB;QAC/C,OAAO;YACL,GAAG,WAAW;YACd,aAAa,EAAE;YACf,eAAe,IAAM;YACrB,kBAAkB,IAAM;YACxB,mBAAmB,IAAM;QAC3B;IACF;IAEA,OAAO;AACT;IAhBgB;;QACY,iJAAA,CAAA,aAAU;QAChB;;;AAiBf,MAAM,6BAA6B;IACxC,sBAAsB;IACtB,kBAAkB;IAClB,gBAAgB;IAChB,kBAAkB;IAElB,kBAAkB;IAClB,cAAc;IACd,YAAY;IACZ,cAAc;IACd,gBAAgB;IAEhB,qBAAqB;IACrB,iBAAiB;IACjB,eAAe;IACf,iBAAiB;IAEjB,sBAAsB;IACtB,kBAAkB;IAClB,gBAAgB;IAChB,kBAAkB;IAElB,UAAU;IACV,cAAc;IAEd,aAAa;IACb,iBAAiB;AACnB;AAGO,MAAM,gCAAgC;IAC3C,uBAAuB;IACvB,gBAAgB;IAChB,kBAAkB;IAElB,sBAAsB;IACtB,wBAAwB;IACxB,gBAAgB;IAChB,gBAAgB;IAEhB,kBAAkB;IAClB,YAAY;IACZ,aAAa;IAEb,iBAAiB;IACjB,YAAY;IACZ,cAAc;IAEd,qBAAqB;IACrB,gBAAgB;IAChB,kBAAkB;IAElB,oBAAoB;IACpB,eAAe;IACf,iBAAiB;IAEjB,sBAAsB;IACtB,eAAe;IACf,iBAAiB;AACnB;AAGO,SAAS,iBACd,WAAqB,EACrB,mBAAsC;IAEtC,MAAM,WAAW,MAAM,OAAO,CAAC,uBAAuB,sBAAsB;QAAC;KAAoB;IACjG,OAAO,SAAS,KAAK,CAAC,CAAA,aAAc,YAAY,QAAQ,CAAC;AAC3D", "debugId": null}}, {"offset": {"line": 981, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,KAGoC;QAHpC,EAChB,SAAS,EACT,GAAG,OACiD,GAHpC;IAIhB,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,6LAAC,2MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B;KArBS", "debugId": null}}, {"offset": {"line": 1033, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/loans/LoanComments.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Badge } from '@/components/ui/badge'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { MessageCircle, Send, User, Clock } from 'lucide-react'\nimport { useToast } from '@/hooks/use-toast'\nimport { useSession } from 'next-auth/react'\n\ninterface LoanComment {\n  id: string\n  comment: string\n  isInternal: boolean\n  createdAt: string\n  updatedAt: string\n  user: {\n    id: string\n    firstName: string\n    lastName: string\n    role: string\n  }\n}\n\ninterface LoanCommentsProps {\n  loanId: string\n}\n\nexport default function LoanComments({ loanId }: LoanCommentsProps) {\n  const { data: session } = useSession()\n  const [comments, setComments] = useState<LoanComment[]>([])\n  const [newComment, setNewComment] = useState('')\n  const [isInternal, setIsInternal] = useState(false)\n  const [loading, setLoading] = useState(true)\n  const [submitting, setSubmitting] = useState(false)\n  const { toast } = useToast()\n\n  useEffect(() => {\n    fetchComments()\n  }, [loanId])\n\n  const fetchComments = async () => {\n    try {\n      setLoading(true)\n      const response = await fetch(`/api/loans/${loanId}/comments`)\n      if (response.ok) {\n        const data = await response.json()\n        setComments(data)\n      } else {\n        throw new Error('Failed to fetch comments')\n      }\n    } catch (error) {\n      console.error('Error fetching comments:', error)\n      toast({\n        title: 'Error',\n        description: 'Failed to load comments',\n        variant: 'destructive'\n      })\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleSubmitComment = async () => {\n    if (!newComment.trim()) {\n      toast({\n        title: 'Error',\n        description: 'Please enter a comment',\n        variant: 'destructive'\n      })\n      return\n    }\n\n    try {\n      setSubmitting(true)\n      const response = await fetch(`/api/loans/${loanId}/comments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          comment: newComment.trim(),\n          isInternal\n        })\n      })\n\n      if (response.ok) {\n        const newCommentData = await response.json()\n        setComments(prev => [newCommentData, ...prev])\n        setNewComment('')\n        setIsInternal(false)\n        toast({\n          title: 'Success',\n          description: 'Comment added successfully'\n        })\n      } else {\n        throw new Error('Failed to add comment')\n      }\n    } catch (error) {\n      console.error('Error adding comment:', error)\n      toast({\n        title: 'Error',\n        description: 'Failed to add comment',\n        variant: 'destructive'\n      })\n    } finally {\n      setSubmitting(false)\n    }\n  }\n\n  const getRoleBadgeColor = (role: string) => {\n    switch (role) {\n      case 'SUPER_ADMIN': return 'bg-red-100 text-red-800'\n      case 'HIGHER_MANAGEMENT': return 'bg-purple-100 text-purple-800'\n      case 'MANAGER': return 'bg-blue-100 text-blue-800'\n      case 'CREDIT_OFFICER': return 'bg-green-100 text-green-800'\n      case 'CUSTOMER_SERVICE_OFFICER': return 'bg-yellow-100 text-yellow-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const formatRole = (role: string) => {\n    return role.replace(/_/g, ' ').toLowerCase().replace(/\\b\\w/g, l => l.toUpperCase())\n  }\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <MessageCircle className=\"h-5 w-5\" />\n          Loan Communication\n        </CardTitle>\n        <CardDescription>\n          Communication log for this loan application\n        </CardDescription>\n      </CardHeader>\n      <CardContent className=\"space-y-4\">\n        {/* Add Comment Form */}\n        <div className=\"space-y-3 p-4 border rounded-lg bg-gray-50\">\n          <Textarea\n            placeholder=\"Add a comment or note about this loan...\"\n            value={newComment}\n            onChange={(e) => setNewComment(e.target.value)}\n            rows={3}\n          />\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-2\">\n              <Checkbox\n                id=\"internal\"\n                checked={isInternal}\n                onCheckedChange={(checked) => setIsInternal(checked as boolean)}\n              />\n              <label htmlFor=\"internal\" className=\"text-sm text-gray-600\">\n                Internal comment (staff only)\n              </label>\n            </div>\n            <Button \n              onClick={handleSubmitComment}\n              disabled={submitting || !newComment.trim()}\n              size=\"sm\"\n            >\n              <Send className=\"h-4 w-4 mr-2\" />\n              {submitting ? 'Adding...' : 'Add Comment'}\n            </Button>\n          </div>\n        </div>\n\n        {/* Comments List */}\n        <div className=\"space-y-3\">\n          {loading ? (\n            <div className=\"text-center py-8 text-gray-500\">\n              Loading comments...\n            </div>\n          ) : comments.length === 0 ? (\n            <div className=\"text-center py-8 text-gray-500\">\n              <MessageCircle className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n              <p>No comments yet</p>\n              <p className=\"text-sm\">Be the first to add a comment about this loan</p>\n            </div>\n          ) : (\n            comments.map((comment) => (\n              <div key={comment.id} className=\"border rounded-lg p-4 bg-white\">\n                <div className=\"flex items-start justify-between mb-2\">\n                  <div className=\"flex items-center gap-2\">\n                    <User className=\"h-4 w-4 text-gray-500\" />\n                    <span className=\"font-medium\">\n                      {comment.user.firstName} {comment.user.lastName}\n                    </span>\n                    <Badge className={getRoleBadgeColor(comment.user.role)}>\n                      {formatRole(comment.user.role)}\n                    </Badge>\n                    {comment.isInternal && (\n                      <Badge variant=\"outline\" className=\"text-orange-600 border-orange-600\">\n                        Internal\n                      </Badge>\n                    )}\n                  </div>\n                  <div className=\"flex items-center gap-1 text-sm text-gray-500\">\n                    <Clock className=\"h-3 w-3\" />\n                    <span>\n                      {new Date(comment.createdAt).toLocaleDateString()} {' '}\n                      {new Date(comment.createdAt).toLocaleTimeString([], { \n                        hour: '2-digit', \n                        minute: '2-digit' \n                      })}\n                    </span>\n                  </div>\n                </div>\n                <p className=\"text-gray-700 whitespace-pre-wrap\">{comment.comment}</p>\n              </div>\n            ))\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;AAVA;;;;;;;;;;AA8Be,SAAS,aAAa,KAA6B;QAA7B,EAAE,MAAM,EAAqB,GAA7B;;IACnC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG;QAAC;KAAO;IAEX,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,AAAC,cAAoB,OAAP,QAAO;YAClD,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,YAAY;YACd,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,WAAW,IAAI,IAAI;YACtB,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;YACA;QACF;QAEA,IAAI;YACF,cAAc;YACd,MAAM,WAAW,MAAM,MAAM,AAAC,cAAoB,OAAP,QAAO,cAAY;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS,WAAW,IAAI;oBACxB;gBACF;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,iBAAiB,MAAM,SAAS,IAAI;gBAC1C,YAAY,CAAA,OAAQ;wBAAC;2BAAmB;qBAAK;gBAC7C,cAAc;gBACd,cAAc;gBACd,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAqB,OAAO;YACjC,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAkB,OAAO;YAC9B,KAAK;gBAA4B,OAAO;YACxC;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,OAAO,CAAC,MAAM,KAAK,WAAW,GAAG,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;IAClF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;;kCACT,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,2NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAGvC,6LAAC,mIAAA,CAAA,kBAAe;kCAAC;;;;;;;;;;;;0BAInB,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uIAAA,CAAA,WAAQ;gCACP,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,MAAM;;;;;;0CAER,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uIAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,SAAS;gDACT,iBAAiB,CAAC,UAAY,cAAc;;;;;;0DAE9C,6LAAC;gDAAM,SAAQ;gDAAW,WAAU;0DAAwB;;;;;;;;;;;;kDAI9D,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU,cAAc,CAAC,WAAW,IAAI;wCACxC,MAAK;;0DAEL,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,aAAa,cAAc;;;;;;;;;;;;;;;;;;;kCAMlC,6LAAC;wBAAI,WAAU;kCACZ,wBACC,6LAAC;4BAAI,WAAU;sCAAiC;;;;;mCAG9C,SAAS,MAAM,KAAK,kBACtB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,6LAAC;8CAAE;;;;;;8CACH,6LAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;mCAGzB,SAAS,GAAG,CAAC,CAAC,wBACZ,6LAAC;gCAAqB,WAAU;;kDAC9B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;;4DACb,QAAQ,IAAI,CAAC,SAAS;4DAAC;4DAAE,QAAQ,IAAI,CAAC,QAAQ;;;;;;;kEAEjD,6LAAC,oIAAA,CAAA,QAAK;wDAAC,WAAW,kBAAkB,QAAQ,IAAI,CAAC,IAAI;kEAClD,WAAW,QAAQ,IAAI,CAAC,IAAI;;;;;;oDAE9B,QAAQ,UAAU,kBACjB,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;kEAAoC;;;;;;;;;;;;0DAK3E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,6LAAC;;4DACE,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB;4DAAG;4DAAE;4DACnD,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB,CAAC,EAAE,EAAE;gEAClD,MAAM;gEACN,QAAQ;4DACV;;;;;;;;;;;;;;;;;;;kDAIN,6LAAC;wCAAE,WAAU;kDAAqC,QAAQ,OAAO;;;;;;;+BA3BzD,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;AAmClC;GA5LwB;;QACI,iJAAA,CAAA,aAAU;QAMlB,+HAAA,CAAA,WAAQ;;;KAPJ", "debugId": null}}, {"offset": {"line": 1453, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/loan-activity-log.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport {\n  Clock, \n  User, \n  FileText, \n  CheckCircle, \n  XCircle, \n  AlertCircle, \n  DollarSign,\n  Calendar,\n  Edit,\n  Plus,\n  Trash2,\n  Eye,\n  Download,\n  Upload,\n  RefreshCw\n} from 'lucide-react'\nimport { formatDistanceToNow } from 'date-fns'\n\n// Simple avatar component for initials\nconst UserAvatar = ({ name, className = \"\" }: { name: string, className?: string }) => {\n  const initials = name\n    .split(' ')\n    .map(n => n.charAt(0))\n    .join('')\n    .toUpperCase()\n    .slice(0, 2)\n\n  return (\n    <div className={`flex items-center justify-center bg-blue-500 text-white rounded-full font-medium ${className}`}>\n      {initials}\n    </div>\n  )\n}\n\ninterface ActivityLogEntry {\n  id: string\n  action: string\n  resource: string\n  resourceId: string\n  details: string\n  timestamp: string\n  user: {\n    id: string\n    firstName: string\n    lastName: string\n    role: string\n    email: string\n  }\n}\n\ninterface LoanActivityLogProps {\n  loanId: string\n}\n\nconst getActivityIcon = (action: string, details: string) => {\n  const actionLower = action.toLowerCase()\n  const detailsLower = details.toLowerCase()\n  \n  if (actionLower === 'create') return <Plus className=\"h-4 w-4\" />\n  if (actionLower === 'update') {\n    if (detailsLower.includes('approved')) return <CheckCircle className=\"h-4 w-4\" />\n    if (detailsLower.includes('rejected')) return <XCircle className=\"h-4 w-4\" />\n    if (detailsLower.includes('disbursed')) return <DollarSign className=\"h-4 w-4\" />\n    if (detailsLower.includes('schedule')) return <Calendar className=\"h-4 w-4\" />\n    if (detailsLower.includes('edit')) return <Edit className=\"h-4 w-4\" />\n    return <RefreshCw className=\"h-4 w-4\" />\n  }\n  if (actionLower === 'delete') return <Trash2 className=\"h-4 w-4\" />\n  if (actionLower === 'view') return <Eye className=\"h-4 w-4\" />\n  if (actionLower === 'download') return <Download className=\"h-4 w-4\" />\n  if (actionLower === 'upload') return <Upload className=\"h-4 w-4\" />\n  \n  return <FileText className=\"h-4 w-4\" />\n}\n\nconst getActivityColor = (action: string, details: string) => {\n  const actionLower = action.toLowerCase()\n  const detailsLower = details.toLowerCase()\n  \n  if (actionLower === 'create') return 'bg-blue-500'\n  if (actionLower === 'update') {\n    if (detailsLower.includes('approved')) return 'bg-green-500'\n    if (detailsLower.includes('rejected')) return 'bg-red-500'\n    if (detailsLower.includes('disbursed')) return 'bg-emerald-500'\n    if (detailsLower.includes('schedule')) return 'bg-purple-500'\n    return 'bg-orange-500'\n  }\n  if (actionLower === 'delete') return 'bg-red-500'\n  \n  return 'bg-gray-500'\n}\n\nconst getRoleBadgeColor = (role: string) => {\n  switch (role) {\n    case 'SUPER_ADMIN': return 'bg-purple-100 text-purple-800 border-purple-200'\n    case 'MANAGER': return 'bg-blue-100 text-blue-800 border-blue-200'\n    case 'CREDIT_OFFICER': return 'bg-green-100 text-green-800 border-green-200'\n    case 'FIELD_OFFICER': return 'bg-yellow-100 text-yellow-800 border-yellow-200'\n    default: return 'bg-gray-100 text-gray-800 border-gray-200'\n  }\n}\n\nconst formatRoleName = (role: string) => {\n  return role.split('_').map(word =>\n    word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()\n  ).join(' ')\n}\n\n// Function to parse and format activity details\nconst formatActivityDetails = (action: string, details: string, resource: string) => {\n  // If details is a JSON string, try to parse it\n  try {\n    const parsed = JSON.parse(details)\n\n    // Format based on action type\n    switch (action) {\n      case 'APPROVE':\n        if (parsed.status === 'APPROVED') {\n          return `Loan approved successfully. ${parsed.loanType ? `Loan Type: ${parsed.loanType}` : ''} ${parsed.customerName ? `Customer: ${parsed.customerName}` : ''} ${parsed.loanNumber ? `Loan Number: ${parsed.loanNumber}` : ''}`\n        }\n        break\n\n      case 'DISBURSE':\n        if (parsed.status === 'ACTIVE') {\n          const amount = parsed.disbursedAmount ? `Amount: LKR ${Number(parsed.disbursedAmount).toLocaleString()}` : ''\n          const method = parsed.disbursementMethod ? `Method: ${parsed.disbursementMethod}` : ''\n          const reference = parsed.disbursementReference ? `Reference: ${parsed.disbursementReference}` : ''\n          return `Loan disbursed successfully. ${[amount, method, reference].filter(Boolean).join(', ')}`\n        }\n        break\n\n      case 'CREATE':\n        if (resource === 'Loan') {\n          const amount = parsed.principalAmount ? `Amount: LKR ${Number(parsed.principalAmount).toLocaleString()}` : ''\n          const customer = parsed.customerName ? `Customer: ${parsed.customerName}` : ''\n          const loanType = parsed.loanType ? `Type: ${parsed.loanType}` : ''\n          return `New loan application created. ${[customer, amount, loanType].filter(Boolean).join(', ')}`\n        }\n        break\n\n      case 'UPDATE':\n        if (parsed.status) {\n          return `Loan status updated to ${parsed.status}. ${parsed.loanNumber ? `Loan: ${parsed.loanNumber}` : ''}`\n        }\n        break\n\n      case 'CREATE_COMMENT':\n        return `Added ${parsed.isInternal ? 'internal' : 'public'} comment: \"${parsed.comment}\"`\n\n      default:\n        // Try to format common fields\n        const commonFields = []\n        if (parsed.status) commonFields.push(`Status: ${parsed.status}`)\n        if (parsed.amount) commonFields.push(`Amount: LKR ${Number(parsed.amount).toLocaleString()}`)\n        if (parsed.loanNumber) commonFields.push(`Loan: ${parsed.loanNumber}`)\n        if (parsed.customerName) commonFields.push(`Customer: ${parsed.customerName}`)\n\n        if (commonFields.length > 0) {\n          return commonFields.join(', ')\n        }\n    }\n  } catch (e) {\n    // If not JSON or parsing fails, return the original details\n  }\n\n  // Fallback: clean up the raw details string\n  if (details.startsWith('{\"') && details.endsWith('\"}')) {\n    try {\n      const parsed = JSON.parse(details)\n      const entries = Object.entries(parsed)\n        .filter(([key, value]) => value !== null && value !== undefined && value !== '')\n        .map(([key, value]) => {\n          const formattedKey = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())\n          if (key.includes('Amount') && typeof value === 'number') {\n            return `${formattedKey}: LKR ${value.toLocaleString()}`\n          }\n          return `${formattedKey}: ${value}`\n        })\n\n      if (entries.length > 0) {\n        return entries.join(', ')\n      }\n    } catch (e) {\n      // Continue to fallback\n    }\n  }\n\n  return details\n}\n\nexport default function LoanActivityLog({ loanId }: LoanActivityLogProps) {\n  const [activities, setActivities] = useState<ActivityLogEntry[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  useEffect(() => {\n    fetchActivities()\n  }, [loanId])\n\n  const fetchActivities = async () => {\n    try {\n      setLoading(true)\n      const response = await fetch(`/api/loans/${loanId}/activities`)\n      \n      if (response.ok) {\n        const data = await response.json()\n        setActivities(data)\n      } else {\n        setError('Failed to fetch loan activities')\n      }\n    } catch (error) {\n      console.error('Error fetching activities:', error)\n      setError('Failed to fetch loan activities')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (loading) {\n    return (\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Clock className=\"h-5 w-5\" />\n            Loan Activity Log\n          </CardTitle>\n          <CardDescription>Complete history of all loan activities</CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex items-center justify-center py-8\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\n          </div>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  if (error) {\n    return (\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Clock className=\"h-5 w-5\" />\n            Loan Activity Log\n          </CardTitle>\n          <CardDescription>Complete history of all loan activities</CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex items-center justify-center py-8 text-red-600\">\n            <AlertCircle className=\"h-5 w-5 mr-2\" />\n            {error}\n          </div>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <Clock className=\"h-5 w-5\" />\n          Loan Activity Log\n        </CardTitle>\n        <CardDescription>\n          Complete history of all loan activities - {activities.length} entries\n        </CardDescription>\n      </CardHeader>\n      <CardContent>\n        {activities.length === 0 ? (\n          <div className=\"flex items-center justify-center py-8 text-gray-500\">\n            <FileText className=\"h-5 w-5 mr-2\" />\n            No activities recorded yet\n          </div>\n        ) : (\n          <div className=\"relative\">\n            {/* Timeline line */}\n            <div className=\"absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200\"></div>\n            \n            <div className=\"space-y-6\">\n              {activities.map((activity, index) => (\n                <div key={activity.id} className=\"relative flex items-start gap-4\">\n                  {/* Timeline dot */}\n                  <div className={`relative z-10 flex items-center justify-center w-12 h-12 rounded-full ${getActivityColor(activity.action, activity.details)} text-white shadow-lg`}>\n                    {getActivityIcon(activity.action, activity.details)}\n                  </div>\n                  \n                  {/* Activity content */}\n                  <div className=\"flex-1 min-w-0 bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow\">\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center gap-2 mb-2\">\n                          <h4 className=\"font-semibold text-gray-900\">\n                            {activity.action} {activity.resource}\n                          </h4>\n                          <Badge\n                            variant=\"outline\"\n                            className={getRoleBadgeColor(activity.user.role)}\n                          >\n                            {formatRoleName(activity.user.role)}\n                          </Badge>\n                        </div>\n                        \n                        <p className=\"text-gray-700 mb-3\">\n                          {formatActivityDetails(activity.action, activity.details, activity.resource)}\n                        </p>\n                        \n                        <div className=\"flex items-center gap-4 text-sm text-gray-500\">\n                          <div className=\"flex items-center gap-2\">\n                            <UserAvatar\n                              name={`${activity.user.firstName} ${activity.user.lastName}`}\n                              className=\"h-6 w-6 text-xs\"\n                            />\n                            <span className=\"font-medium\">\n                              {activity.user.firstName} {activity.user.lastName}\n                            </span>\n                          </div>\n                          \n                          <div className=\"flex items-center gap-1\">\n                            <Clock className=\"h-3 w-3\" />\n                            <span>\n                              {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}\n                            </span>\n                          </div>\n\n                          <div className=\"text-xs text-gray-400\">\n                            {new Date(activity.timestamp).toLocaleString()}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;;;AAtBA;;;;;;AAwBA,uCAAuC;AACvC,MAAM,aAAa;QAAC,EAAE,IAAI,EAAE,YAAY,EAAE,EAAwC;IAChF,MAAM,WAAW,KACd,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM,CAAC,IAClB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IAEZ,qBACE,6LAAC;QAAI,WAAW,AAAC,oFAA6F,OAAV;kBACjG;;;;;;AAGP;KAbM;AAmCN,MAAM,kBAAkB,CAAC,QAAgB;IACvC,MAAM,cAAc,OAAO,WAAW;IACtC,MAAM,eAAe,QAAQ,WAAW;IAExC,IAAI,gBAAgB,UAAU,qBAAO,6LAAC,qMAAA,CAAA,OAAI;QAAC,WAAU;;;;;;IACrD,IAAI,gBAAgB,UAAU;QAC5B,IAAI,aAAa,QAAQ,CAAC,aAAa,qBAAO,6LAAC,8NAAA,CAAA,cAAW;YAAC,WAAU;;;;;;QACrE,IAAI,aAAa,QAAQ,CAAC,aAAa,qBAAO,6LAAC,+MAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QACjE,IAAI,aAAa,QAAQ,CAAC,cAAc,qBAAO,6LAAC,qNAAA,CAAA,aAAU;YAAC,WAAU;;;;;;QACrE,IAAI,aAAa,QAAQ,CAAC,aAAa,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAClE,IAAI,aAAa,QAAQ,CAAC,SAAS,qBAAO,6LAAC,8MAAA,CAAA,OAAI;YAAC,WAAU;;;;;;QAC1D,qBAAO,6LAAC,mNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;IAC9B;IACA,IAAI,gBAAgB,UAAU,qBAAO,6LAAC,6MAAA,CAAA,SAAM;QAAC,WAAU;;;;;;IACvD,IAAI,gBAAgB,QAAQ,qBAAO,6LAAC,mMAAA,CAAA,MAAG;QAAC,WAAU;;;;;;IAClD,IAAI,gBAAgB,YAAY,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;QAAC,WAAU;;;;;;IAC3D,IAAI,gBAAgB,UAAU,qBAAO,6LAAC,yMAAA,CAAA,SAAM;QAAC,WAAU;;;;;;IAEvD,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;QAAC,WAAU;;;;;;AAC7B;AAEA,MAAM,mBAAmB,CAAC,QAAgB;IACxC,MAAM,cAAc,OAAO,WAAW;IACtC,MAAM,eAAe,QAAQ,WAAW;IAExC,IAAI,gBAAgB,UAAU,OAAO;IACrC,IAAI,gBAAgB,UAAU;QAC5B,IAAI,aAAa,QAAQ,CAAC,aAAa,OAAO;QAC9C,IAAI,aAAa,QAAQ,CAAC,aAAa,OAAO;QAC9C,IAAI,aAAa,QAAQ,CAAC,cAAc,OAAO;QAC/C,IAAI,aAAa,QAAQ,CAAC,aAAa,OAAO;QAC9C,OAAO;IACT;IACA,IAAI,gBAAgB,UAAU,OAAO;IAErC,OAAO;AACT;AAEA,MAAM,oBAAoB,CAAC;IACzB,OAAQ;QACN,KAAK;YAAe,OAAO;QAC3B,KAAK;YAAW,OAAO;QACvB,KAAK;YAAkB,OAAO;QAC9B,KAAK;YAAiB,OAAO;QAC7B;YAAS,OAAO;IAClB;AACF;AAEA,MAAM,iBAAiB,CAAC;IACtB,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OACzB,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,GAAG,WAAW,IACxD,IAAI,CAAC;AACT;AAEA,gDAAgD;AAChD,MAAM,wBAAwB,CAAC,QAAgB,SAAiB;IAC9D,+CAA+C;IAC/C,IAAI;QACF,MAAM,SAAS,KAAK,KAAK,CAAC;QAE1B,8BAA8B;QAC9B,OAAQ;YACN,KAAK;gBACH,IAAI,OAAO,MAAM,KAAK,YAAY;oBAChC,OAAO,AAAC,+BAAwF,OAA1D,OAAO,QAAQ,GAAG,AAAC,cAA6B,OAAhB,OAAO,QAAQ,IAAK,IAAG,KAAoE,OAAjE,OAAO,YAAY,GAAG,AAAC,aAAgC,OAApB,OAAO,YAAY,IAAK,IAAG,KAAgE,OAA7D,OAAO,UAAU,GAAG,AAAC,gBAAiC,OAAlB,OAAO,UAAU,IAAK;gBAC7N;gBACA;YAEF,KAAK;gBACH,IAAI,OAAO,MAAM,KAAK,UAAU;oBAC9B,MAAM,SAAS,OAAO,eAAe,GAAG,AAAC,eAA8D,OAAhD,OAAO,OAAO,eAAe,EAAE,cAAc,MAAO;oBAC3G,MAAM,SAAS,OAAO,kBAAkB,GAAG,AAAC,WAAoC,OAA1B,OAAO,kBAAkB,IAAK;oBACpF,MAAM,YAAY,OAAO,qBAAqB,GAAG,AAAC,cAA0C,OAA7B,OAAO,qBAAqB,IAAK;oBAChG,OAAO,AAAC,gCAAsF,OAAvD;wBAAC;wBAAQ;wBAAQ;qBAAU,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;gBAC1F;gBACA;YAEF,KAAK;gBACH,IAAI,aAAa,QAAQ;oBACvB,MAAM,SAAS,OAAO,eAAe,GAAG,AAAC,eAA8D,OAAhD,OAAO,OAAO,eAAe,EAAE,cAAc,MAAO;oBAC3G,MAAM,WAAW,OAAO,YAAY,GAAG,AAAC,aAAgC,OAApB,OAAO,YAAY,IAAK;oBAC5E,MAAM,WAAW,OAAO,QAAQ,GAAG,AAAC,SAAwB,OAAhB,OAAO,QAAQ,IAAK;oBAChE,OAAO,AAAC,iCAAwF,OAAxD;wBAAC;wBAAU;wBAAQ;qBAAS,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;gBAC5F;gBACA;YAEF,KAAK;gBACH,IAAI,OAAO,MAAM,EAAE;oBACjB,OAAO,AAAC,0BAA2C,OAAlB,OAAO,MAAM,EAAC,MAA0D,OAAtD,OAAO,UAAU,GAAG,AAAC,SAA0B,OAAlB,OAAO,UAAU,IAAK;gBACxG;gBACA;YAEF,KAAK;gBACH,OAAO,AAAC,SAA+D,OAAvD,OAAO,UAAU,GAAG,aAAa,UAAS,eAA4B,OAAf,OAAO,OAAO,EAAC;YAExF;gBACE,8BAA8B;gBAC9B,MAAM,eAAe,EAAE;gBACvB,IAAI,OAAO,MAAM,EAAE,aAAa,IAAI,CAAC,AAAC,WAAwB,OAAd,OAAO,MAAM;gBAC7D,IAAI,OAAO,MAAM,EAAE,aAAa,IAAI,CAAC,AAAC,eAAqD,OAAvC,OAAO,OAAO,MAAM,EAAE,cAAc;gBACxF,IAAI,OAAO,UAAU,EAAE,aAAa,IAAI,CAAC,AAAC,SAA0B,OAAlB,OAAO,UAAU;gBACnE,IAAI,OAAO,YAAY,EAAE,aAAa,IAAI,CAAC,AAAC,aAAgC,OAApB,OAAO,YAAY;gBAE3E,IAAI,aAAa,MAAM,GAAG,GAAG;oBAC3B,OAAO,aAAa,IAAI,CAAC;gBAC3B;QACJ;IACF,EAAE,OAAO,GAAG;IACV,4DAA4D;IAC9D;IAEA,4CAA4C;IAC5C,IAAI,QAAQ,UAAU,CAAC,SAAS,QAAQ,QAAQ,CAAC,OAAO;QACtD,IAAI;YACF,MAAM,SAAS,KAAK,KAAK,CAAC;YAC1B,MAAM,UAAU,OAAO,OAAO,CAAC,QAC5B,MAAM,CAAC;oBAAC,CAAC,KAAK,MAAM;uBAAK,UAAU,QAAQ,UAAU,aAAa,UAAU;eAC5E,GAAG,CAAC;oBAAC,CAAC,KAAK,MAAM;gBAChB,MAAM,eAAe,IAAI,OAAO,CAAC,YAAY,OAAO,OAAO,CAAC,MAAM,CAAA,MAAO,IAAI,WAAW;gBACxF,IAAI,IAAI,QAAQ,CAAC,aAAa,OAAO,UAAU,UAAU;oBACvD,OAAO,AAAC,GAAuB,OAArB,cAAa,UAA+B,OAAvB,MAAM,cAAc;gBACrD;gBACA,OAAO,AAAC,GAAmB,OAAjB,cAAa,MAAU,OAAN;YAC7B;YAEF,IAAI,QAAQ,MAAM,GAAG,GAAG;gBACtB,OAAO,QAAQ,IAAI,CAAC;YACtB;QACF,EAAE,OAAO,GAAG;QACV,uBAAuB;QACzB;IACF;IAEA,OAAO;AACT;AAEe,SAAS,gBAAgB,KAAgC;QAAhC,EAAE,MAAM,EAAwB,GAAhC;;IACtC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACnE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG;QAAC;KAAO;IAEX,MAAM,kBAAkB;QACtB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,AAAC,cAAoB,OAAP,QAAO;YAElD,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,cAAc;YAChB,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC,mIAAA,CAAA,OAAI;;8BACH,6LAAC,mIAAA,CAAA,aAAU;;sCACT,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAG/B,6LAAC,mIAAA,CAAA,kBAAe;sCAAC;;;;;;;;;;;;8BAEnB,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;IAKzB;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC,mIAAA,CAAA,OAAI;;8BACH,6LAAC,mIAAA,CAAA,aAAU;;sCACT,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAY;;;;;;;sCAG/B,6LAAC,mIAAA,CAAA,kBAAe;sCAAC;;;;;;;;;;;;8BAEnB,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;4BACtB;;;;;;;;;;;;;;;;;;IAKX;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;;kCACT,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;4BAAY;;;;;;;kCAG/B,6LAAC,mIAAA,CAAA,kBAAe;;4BAAC;4BAC4B,WAAW,MAAM;4BAAC;;;;;;;;;;;;;0BAGjE,6LAAC,mIAAA,CAAA,cAAW;0BACT,WAAW,MAAM,KAAK,kBACrB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,iNAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAAiB;;;;;;yCAIvC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;;;;;sCAEf,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,6LAAC;oCAAsB,WAAU;;sDAE/B,6LAAC;4CAAI,WAAW,AAAC,yEAA4H,OAApD,iBAAiB,SAAS,MAAM,EAAE,SAAS,OAAO,GAAE;sDAC1I,gBAAgB,SAAS,MAAM,EAAE,SAAS,OAAO;;;;;;sDAIpD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;;wEACX,SAAS,MAAM;wEAAC;wEAAE,SAAS,QAAQ;;;;;;;8EAEtC,6LAAC,oIAAA,CAAA,QAAK;oEACJ,SAAQ;oEACR,WAAW,kBAAkB,SAAS,IAAI,CAAC,IAAI;8EAE9C,eAAe,SAAS,IAAI,CAAC,IAAI;;;;;;;;;;;;sEAItC,6LAAC;4DAAE,WAAU;sEACV,sBAAsB,SAAS,MAAM,EAAE,SAAS,OAAO,EAAE,SAAS,QAAQ;;;;;;sEAG7E,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EACC,MAAM,AAAC,GAA6B,OAA3B,SAAS,IAAI,CAAC,SAAS,EAAC,KAA0B,OAAvB,SAAS,IAAI,CAAC,QAAQ;4EAC1D,WAAU;;;;;;sFAEZ,6LAAC;4EAAK,WAAU;;gFACb,SAAS,IAAI,CAAC,SAAS;gFAAC;gFAAE,SAAS,IAAI,CAAC,QAAQ;;;;;;;;;;;;;8EAIrD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;sFACjB,6LAAC;sFACE,CAAA,GAAA,qJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,SAAS,SAAS,GAAG;gFAAE,WAAW;4EAAK;;;;;;;;;;;;8EAIzE,6LAAC;oEAAI,WAAU;8EACZ,IAAI,KAAK,SAAS,SAAS,EAAE,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCA7C9C,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2DrC;GAtJwB;MAAA", "debugId": null}}, {"offset": {"line": 2109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/layout/PageHeader.tsx"], "sourcesContent": ["'use client'\n\nimport { useSession, signOut } from 'next-auth/react'\nimport { Button } from '@/components/ui/button'\nimport { ArrowLeft, LogOut, User, Home } from 'lucide-react'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { useState, useEffect } from 'react'\n\ninterface PageHeaderProps {\n  title: string\n  description?: string\n  showBackButton?: boolean\n  backUrl?: string\n  actions?: React.ReactNode\n  children?: React.ReactNode\n}\n\nexport default function PageHeader({\n  title,\n  description,\n  showBackButton = true,\n  backUrl = '/dashboard',\n  actions,\n  children\n}: PageHeaderProps) {\n  const { data: session } = useSession()\n  const router = useRouter()\n  const [companySettings, setCompanySettings] = useState({\n    systemTitle: 'Nilgala Micro',\n    companyLogo: ''\n  })\n\n  useEffect(() => {\n    fetchCompanySettings()\n  }, [])\n\n  const fetchCompanySettings = async () => {\n    try {\n      const response = await fetch('/api/company-settings')\n      if (response.ok) {\n        const data = await response.json()\n        setCompanySettings({\n          systemTitle: data.systemTitle || 'Nilgala Micro',\n          companyLogo: data.companyLogo || ''\n        })\n      }\n    } catch (error) {\n      console.error('Error fetching company settings:', error)\n    }\n  }\n\n  const handleBack = () => {\n    if (backUrl) {\n      router.push(backUrl)\n    } else {\n      router.back()\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Top Navigation Bar */}\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-3 sm:py-4\">\n            <div className=\"flex items-center space-x-2 sm:space-x-4 min-w-0 flex-1\">\n              <Link href=\"/dashboard\" className=\"flex items-center space-x-2 min-w-0\">\n                {companySettings.companyLogo ? (\n                  <img\n                    src={companySettings.companyLogo}\n                    alt=\"Company Logo\"\n                    className=\"h-6 sm:h-8 object-contain flex-shrink-0\"\n                    onError={(e) => {\n                      e.currentTarget.style.display = 'none'\n                    }}\n                  />\n                ) : null}\n                {!companySettings.companyLogo && (\n                  <h1 className=\"text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 truncate\">\n                    {companySettings.systemTitle}\n                  </h1>\n                )}\n              </Link>\n              {showBackButton && (\n                <div className=\"flex items-center space-x-1 sm:space-x-2\">\n                  <span className=\"text-gray-400 hidden sm:inline\">|</span>\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={handleBack}\n                    className=\"text-gray-600 hover:text-gray-900 px-2 sm:px-3\"\n                  >\n                    <ArrowLeft className=\"h-4 w-4 mr-1 sm:mr-2\" />\n                    <span className=\"hidden sm:inline\">Back</span>\n                  </Button>\n                </div>\n              )}\n            </div>\n\n            <div className=\"flex items-center space-x-2 sm:space-x-4 flex-shrink-0\">\n              <Link href=\"/dashboard\" className=\"hidden sm:block\">\n                <Button variant=\"ghost\" size=\"sm\">\n                  <Home className=\"h-4 w-4 mr-2\" />\n                  Dashboard\n                </Button>\n              </Link>\n\n              {/* Mobile Dashboard Link */}\n              <Link href=\"/dashboard\" className=\"sm:hidden\">\n                <Button variant=\"ghost\" size=\"sm\" className=\"px-2\">\n                  <Home className=\"h-4 w-4\" />\n                </Button>\n              </Link>\n\n              {session?.user && (\n                <>\n                  {/* Desktop User Info */}\n                  <div className=\"hidden lg:flex items-center space-x-2\">\n                    <User className=\"h-5 w-5 text-gray-500\" />\n                    <span className=\"text-sm text-gray-700\">\n                      {session.user.firstName} {session.user.lastName}\n                    </span>\n                    <span className=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\">\n                      {session.user.role.replace('_', ' ')}\n                    </span>\n                  </div>\n\n                  {/* Mobile User Info */}\n                  <div className=\"lg:hidden flex items-center space-x-1\">\n                    <User className=\"h-4 w-4 text-gray-500\" />\n                    <span className=\"text-xs bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded\">\n                      {session.user.role.replace('_', ' ').split(' ')[0]}\n                    </span>\n                  </div>\n\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => signOut({ callbackUrl: '/auth/signin' })}\n                    className=\"px-2 sm:px-3\"\n                  >\n                    <LogOut className=\"h-4 w-4 mr-0 sm:mr-2\" />\n                    <span className=\"hidden sm:inline\">Sign Out</span>\n                  </Button>\n                </>\n              )}\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Page Content */}\n      <main className=\"max-w-7xl mx-auto py-4 sm:py-6 px-4 sm:px-6 lg:px-8\">\n        <div className=\"py-4 sm:py-6\">\n          {/* Page Title Section */}\n          <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-4 mb-4 sm:mb-6\">\n            <div className=\"min-w-0 flex-1\">\n              <h1 className=\"text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 truncate\">{title}</h1>\n              {description && (\n                <p className=\"text-sm sm:text-base text-gray-600 mt-1\">{description}</p>\n              )}\n            </div>\n            {actions && (\n              <div className=\"flex items-center space-x-2 flex-shrink-0\">\n                {actions}\n              </div>\n            )}\n          </div>\n\n          {/* Page Content */}\n          {children}\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAPA;;;;;;;AAkBe,SAAS,WAAW,KAOjB;QAPiB,EACjC,KAAK,EACL,WAAW,EACX,iBAAiB,IAAI,EACrB,UAAU,YAAY,EACtB,OAAO,EACP,QAAQ,EACQ,GAPiB;;IAQjC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrD,aAAa;QACb,aAAa;IACf;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;QACF;+BAAG,EAAE;IAEL,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,mBAAmB;oBACjB,aAAa,KAAK,WAAW,IAAI;oBACjC,aAAa,KAAK,WAAW,IAAI;gBACnC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,SAAS;YACX,OAAO,IAAI,CAAC;QACd,OAAO;YACL,OAAO,IAAI;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;;4CAC/B,gBAAgB,WAAW,iBAC1B,6LAAC;gDACC,KAAK,gBAAgB,WAAW;gDAChC,KAAI;gDACJ,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;gDAClC;;;;;uDAEA;4CACH,CAAC,gBAAgB,WAAW,kBAC3B,6LAAC;gDAAG,WAAU;0DACX,gBAAgB,WAAW;;;;;;;;;;;;oCAIjC,gCACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAiC;;;;;;0DACjD,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,6LAAC;wDAAK,WAAU;kEAAmB;;;;;;;;;;;;;;;;;;;;;;;;0CAM3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAChC,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;;8DAC3B,6LAAC,sMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAMrC,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAChC,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;sDAC1C,cAAA,6LAAC,sMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;oCAInB,CAAA,oBAAA,8BAAA,QAAS,IAAI,mBACZ;;0DAEE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;;4DACb,QAAQ,IAAI,CAAC,SAAS;4DAAC;4DAAE,QAAQ,IAAI,CAAC,QAAQ;;;;;;;kEAEjD,6LAAC;wDAAK,WAAU;kEACb,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;0DAKpC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEACb,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;0DAItD,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;wDAAE,aAAa;oDAAe;gDACrD,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;wDAAK,WAAU;kEAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUjD,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAoE;;;;;;wCACjF,6BACC,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;gCAG3D,yBACC,6LAAC;oCAAI,WAAU;8CACZ;;;;;;;;;;;;wBAMN;;;;;;;;;;;;;;;;;;AAKX;GA9JwB;;QAQI,iJAAA,CAAA,aAAU;QACrB,qIAAA,CAAA,YAAS;;;KATF", "debugId": null}}, {"offset": {"line": 2511, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/app/loans/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'\nimport { useSession } from 'next-auth/react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow\n} from '@/components/ui/table'\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Input } from '@/components/ui/input'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { useToast } from '@/hooks/use-toast'\nimport { usePermissions } from '@/hooks/usePermissions'\nimport LoanComments from '@/components/loans/LoanComments'\nimport LoanActivityLog from '@/components/loan-activity-log'\nimport {\n  ArrowLeft,\n  User,\n  DollarSign,\n  Calendar,\n  FileText,\n  Shield,\n  CreditCard,\n  Edit,\n  Download,\n  Eye,\n  Plus,\n  CheckCircle,\n  XCircle,\n  AlertCircle,\n  Banknote\n} from 'lucide-react'\nimport Link from 'next/link'\nimport PageHeader from '@/components/layout/PageHeader'\nimport { formatCurrency } from '@/lib/utils'\n\ninterface LoanDetails {\n  id: string\n  loanNumber: string\n  principalAmount: number | string\n  interestRate: number | string\n  tenure: number | string\n  repaymentFrequency: string\n  status: string\n  createdAt: string\n  disbursementDate: string\n  summary: {\n    totalPaid: number\n    outstandingAmount: number\n    paymentProgress: number\n  }\n  customer: {\n    id: string\n    firstName: string\n    lastName: string\n    phone: string\n    email: string\n    nationalId: string\n    address: string\n    monthlyIncome: number | string\n    employmentType: string\n    employer: string\n  }\n  loanType: {\n    name: string\n    interestRate: number | string\n    maxAmount: number | string\n    minAmount: number | string\n    tenureUnit: string\n  }\n  payments: Array<{\n    id: string\n    amount: number | string\n    paymentDate: string\n    paymentMethod: string\n    referenceNumber: string\n    status: string\n    createdByUser?: {\n      id: string\n      firstName: string\n      lastName: string\n      role: string\n    }\n  }>\n  schedules: Array<{\n    id: string\n    installmentNumber: number | string\n    dueDate: string\n    principalAmount: number | string\n    interestAmount: number | string\n    totalAmount: number | string\n    paidAmount: number | string\n    status: string\n  }>\n  guarantors: Array<{\n    id: string\n    guarantorType: string\n    liabilityAmount: number | string\n    guarantorStatus: string\n    customer?: {\n      id: string\n      firstName: string\n      lastName: string\n      phone: string\n    }\n    guarantor?: {\n      id: string\n      firstName: string\n      lastName: string\n      phone: string\n      relationship: string\n    }\n  }>\n  documents: Array<{\n    id: string\n    fileName: string\n    originalName: string\n    documentType: string\n    fileSize: number\n    mimeType: string\n    storageKey: string\n    storageUrl?: string\n    uploadedAt: string\n    createdAt: string\n    isVerified: boolean\n  }>\n}\n\nexport default function LoanDetailsPage() {\n  const params = useParams()\n  const router = useRouter()\n  const { data: session } = useSession()\n  const { toast } = useToast()\n  const { hasPermission } = usePermissions()\n  const [loan, setLoan] = useState<LoanDetails | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [scheduleLoading, setScheduleLoading] = useState(false)\n\n  // Approval dialog state\n  const [approvalDialog, setApprovalDialog] = useState<{\n    isOpen: boolean\n    action: 'APPROVE' | 'REJECT' | 'REQUEST_MORE_INFO' | null\n  }>({\n    isOpen: false,\n    action: null\n  })\n  const [notes, setNotes] = useState('')\n  const [requestedInfo, setRequestedInfo] = useState('')\n  const [processing, setProcessing] = useState(false)\n\n  // Disbursement dialog state\n  const [disbursementDialog, setDisbursementDialog] = useState(false)\n  const [disbursementData, setDisbursementData] = useState({\n    disbursedAmount: 0,\n    disbursementMethod: '',\n    disbursementReference: '',\n    disbursementNotes: '',\n    disbursementDate: new Date().toISOString().split('T')[0]\n  })\n  const [disbursing, setDisbursing] = useState(false)\n\n  useEffect(() => {\n    if (params.id) {\n      fetchLoanDetails(params.id as string)\n    }\n  }, [params.id])\n\n  const fetchLoanDetails = async (loanId: string) => {\n    try {\n      setLoading(true)\n      const response = await fetch(`/api/loans/${loanId}`)\n      if (response.ok) {\n        const data = await response.json()\n\n        // Fetch payment schedule separately\n        const scheduleResponse = await fetch(`/api/loans/${loanId}/schedule`)\n        if (scheduleResponse.ok) {\n          const scheduleData = await scheduleResponse.json()\n          data.schedules = scheduleData\n        }\n\n        setLoan(data)\n      } else {\n        toast({\n          title: 'Error',\n          description: 'Failed to fetch loan details',\n          variant: 'destructive'\n        })\n      }\n    } catch (error) {\n      console.error('Error fetching loan details:', error)\n      toast({\n        title: 'Error',\n        description: 'Failed to fetch loan details',\n        variant: 'destructive'\n      })\n    } finally {\n      setLoading(false)\n    }\n  }\n\n\n\n  const handleApprovalAction = (action: 'APPROVE' | 'REJECT' | 'REQUEST_MORE_INFO') => {\n    setApprovalDialog({\n      isOpen: true,\n      action\n    })\n    setNotes('')\n    setRequestedInfo('')\n  }\n\n  const processApproval = async () => {\n    if (!loan || !approvalDialog.action) return\n\n    try {\n      setProcessing(true)\n      const response = await fetch(`/api/loans/${loan.id}/approve`, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({\n          action: approvalDialog.action,\n          notes,\n          ...(approvalDialog.action === 'REQUEST_MORE_INFO' && { requestedInfo })\n        })\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        toast({\n          title: 'Success',\n          description: data.message\n        })\n\n        // Refresh loan details to show updated status\n        await fetchLoanDetails(loan.id)\n        setApprovalDialog({ isOpen: false, action: null })\n      } else {\n        const error = await response.json()\n        toast({\n          title: 'Error',\n          description: error.error || 'Failed to process approval',\n          variant: 'destructive'\n        })\n      }\n    } catch (error) {\n      console.error('Error processing approval:', error)\n      toast({\n        title: 'Error',\n        description: 'Failed to process approval',\n        variant: 'destructive'\n      })\n    } finally {\n      setProcessing(false)\n    }\n  }\n\n  const getActionButtonProps = (action: string) => {\n    switch (action) {\n      case 'APPROVE':\n        return { variant: 'default' as const, text: 'Approve' }\n      case 'REJECT':\n        return { variant: 'destructive' as const, text: 'Reject' }\n      case 'REQUEST_MORE_INFO':\n        return { variant: 'outline' as const, text: 'Request Info' }\n      default:\n        return { variant: 'default' as const, text: 'Confirm' }\n    }\n  }\n\n  // Generate disbursement reference number based on method\n  const generateDisbursementReference = (method: string) => {\n    const date = new Date()\n    const year = date.getFullYear().toString().slice(-2)\n    const month = (date.getMonth() + 1).toString().padStart(2, '0')\n    const day = date.getDate().toString().padStart(2, '0')\n    const time = date.getTime().toString().slice(-6)\n\n    const prefixes = {\n      'CASH': 'CSH',\n      'BANK_TRANSFER': 'BT',\n      'CHEQUE': 'CHQ',\n      'ONLINE': 'ONL',\n      'MOBILE_PAYMENT': 'MP'\n    }\n\n    const prefix = prefixes[method as keyof typeof prefixes] || 'DSB'\n    return `${prefix}${year}${month}${day}${time}`\n  }\n\n  const handleDisbursement = () => {\n    if (loan) {\n      setDisbursementData(prev => ({\n        ...prev,\n        disbursedAmount: Number(loan.principalAmount)\n      }))\n      setDisbursementDialog(true)\n    }\n  }\n\n  // Handle disbursement method change and auto-generate reference\n  const handleDisbursementMethodChange = (method: string) => {\n    const newReference = generateDisbursementReference(method)\n    setDisbursementData(prev => ({\n      ...prev,\n      disbursementMethod: method,\n      disbursementReference: newReference\n    }))\n  }\n\n  const processDisbursement = async () => {\n    if (!loan) return\n\n    try {\n      setDisbursing(true)\n      const response = await fetch(`/api/loans/${loan.id}/disburse`, {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(disbursementData)\n      })\n\n      if (response.ok) {\n        const data = await response.json()\n        toast({\n          title: 'Success',\n          description: data.message\n        })\n\n        // Refresh loan details to show updated status\n        await fetchLoanDetails(loan.id)\n        setDisbursementDialog(false)\n      } else {\n        const error = await response.json()\n        toast({\n          title: 'Error',\n          description: error.error || 'Failed to disburse loan',\n          variant: 'destructive'\n        })\n      }\n    } catch (error) {\n      console.error('Error disbursing loan:', error)\n      toast({\n        title: 'Error',\n        description: 'Failed to disburse loan',\n        variant: 'destructive'\n      })\n    } finally {\n      setDisbursing(false)\n    }\n  }\n\n  const getStatusBadgeColor = (status: string) => {\n    switch (status) {\n      case 'DRAFT': return 'bg-gray-100 text-gray-800'\n      case 'PENDING': return 'bg-yellow-100 text-yellow-800'\n      case 'APPROVED': return 'bg-blue-100 text-blue-800'\n      case 'DISBURSED': return 'bg-green-100 text-green-800'\n      case 'ACTIVE': return 'bg-green-100 text-green-800'\n      case 'COMPLETED': return 'bg-blue-100 text-blue-800'\n      case 'OVERDUE': return 'bg-red-100 text-red-800'\n      case 'DEFAULTED': return 'bg-red-100 text-red-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getPaymentStatusColor = (status: string) => {\n    switch (status) {\n      case 'COMPLETED': return 'bg-green-100 text-green-800'\n      case 'PENDING': return 'bg-yellow-100 text-yellow-800'\n      case 'FAILED': return 'bg-red-100 text-red-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getScheduleStatusColor = (status: string) => {\n    switch (status) {\n      case 'PAID': return 'bg-green-100 text-green-800'\n      case 'PARTIAL': return 'bg-yellow-100 text-yellow-800'\n      case 'PENDING': return 'bg-gray-100 text-gray-800'\n      case 'OVERDUE': return 'bg-red-100 text-red-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const calculateTotalPaid = () => {\n    if (!loan?.summary) return 0\n    return loan.summary.totalPaid\n  }\n\n  const calculateOutstanding = () => {\n    if (!loan?.summary) return 0\n    return loan.summary.outstandingAmount\n  }\n\n  // Helper function to convert tenure from days to display unit\n  const convertTenureFromDays = (days: number, unit: string): number => {\n    switch (unit) {\n      case 'DAYS': return days\n      case 'WEEKS': return Math.round(days / 7)\n      case 'MONTHS': return Math.round(days / 30)\n      case 'YEARS': return Math.round(days / 365)\n      default: return days\n    }\n  }\n\n  // Helper function to get tenure display text\n  const getTenureDisplayText = () => {\n    if (!loan?.loanType?.tenureUnit) return `${loan?.tenure} months`\n\n    const convertedTenure = convertTenureFromDays(Number(loan.tenure), loan.loanType.tenureUnit)\n    const unit = loan.loanType.tenureUnit.toLowerCase()\n    return `${convertedTenure} ${unit}`\n  }\n\n  const handleViewDocument = (document: any) => {\n    // For now, just open in a new tab using the storage URL or create a preview\n    if (document.storageUrl) {\n      window.open(document.storageUrl, '_blank')\n    } else {\n      // Check if it's a guarantor document or regular document\n      const endpoint = document.isGuarantorDocument\n        ? `/api/guarantor-documents/${document.id}/view`\n        : `/api/documents/${document.id}/view`\n      window.open(endpoint, '_blank')\n    }\n  }\n\n  const handleDownloadDocument = async (document: any) => {\n    try {\n      const endpoint = document.isGuarantorDocument\n        ? `/api/guarantor-documents/${document.id}/download`\n        : `/api/documents/${document.id}/download`\n      const response = await fetch(endpoint)\n      if (response.ok) {\n        const blob = await response.blob()\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.style.display = 'none'\n        a.href = url\n        a.download = document.originalName || document.fileName\n        document.body.appendChild(a)\n        a.click()\n        window.URL.revokeObjectURL(url)\n        document.body.removeChild(a)\n      } else {\n        toast({\n          title: \"Error\",\n          description: \"Failed to download document\",\n          variant: \"destructive\",\n        })\n      }\n    } catch (error) {\n      console.error('Error downloading document:', error)\n      toast({\n        title: \"Error\",\n        description: \"Failed to download document\",\n        variant: \"destructive\",\n      })\n    }\n  }\n\n  if (loading) {\n    return (\n      <PageHeader title=\"Loading...\" description=\"Please wait while we fetch loan details\">\n        <div className=\"flex justify-center items-center h-64\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500\"></div>\n        </div>\n      </PageHeader>\n    )\n  }\n\n  if (!loan) {\n    return (\n      <PageHeader title=\"Loan Not Found\" description=\"The requested loan could not be found\">\n        <div className=\"text-center py-8\">\n          <p className=\"text-gray-500 mb-4\">The loan you're looking for doesn't exist or you don't have permission to view it.</p>\n          <Link href=\"/loans\">\n            <Button>\n              <ArrowLeft className=\"h-4 w-4 mr-2\" />\n              Back to Loans\n            </Button>\n          </Link>\n        </div>\n      </PageHeader>\n    )\n  }\n\n  return (\n    <PageHeader\n      title={`Loan ${loan.loanNumber}`}\n      description={`${loan.customer.firstName} ${loan.customer.lastName} - ${loan.loanType.name}`}\n      actions={\n        <div className=\"flex gap-2\">\n          {loan.status === 'PENDING_APPROVAL' && hasPermission('loans:approve') && (\n            <>\n              <Button\n                variant=\"default\"\n                onClick={() => handleApprovalAction('APPROVE')}\n              >\n                <CheckCircle className=\"h-4 w-4 mr-2\" />\n                Approve\n              </Button>\n              <Button\n                variant=\"destructive\"\n                onClick={() => handleApprovalAction('REJECT')}\n              >\n                <XCircle className=\"h-4 w-4 mr-2\" />\n                Reject\n              </Button>\n              <Button\n                variant=\"outline\"\n                onClick={() => handleApprovalAction('REQUEST_MORE_INFO')}\n              >\n                <AlertCircle className=\"h-4 w-4 mr-2\" />\n                Request Info\n              </Button>\n            </>\n          )}\n          {loan.status === 'PENDING_MORE_INFO' && hasPermission('loans:approve') && (\n            <div className=\"flex items-center gap-2\">\n              <div className=\"flex items-center gap-2 px-3 py-2 bg-yellow-50 border border-yellow-200 rounded-md\">\n                <AlertCircle className=\"h-4 w-4 text-yellow-600\" />\n                <span className=\"text-sm text-yellow-800 font-medium\">\n                  More information requested\n                </span>\n              </div>\n              <Button\n                variant=\"default\"\n                onClick={() => handleApprovalAction('APPROVE')}\n              >\n                <CheckCircle className=\"h-4 w-4 mr-2\" />\n                Approve\n              </Button>\n              <Button\n                variant=\"destructive\"\n                onClick={() => handleApprovalAction('REJECT')}\n              >\n                <XCircle className=\"h-4 w-4 mr-2\" />\n                Reject\n              </Button>\n            </div>\n          )}\n          {loan.status === 'APPROVED' && (\n            <Button\n              variant=\"default\"\n              onClick={handleDisbursement}\n            >\n              <Banknote className=\"h-4 w-4 mr-2\" />\n              Disburse Loan\n            </Button>\n          )}\n          {loan.status === 'PENDING_MORE_INFO' && (\n            <Link href={`/loans/${loan.id}/edit`}>\n              <Button variant=\"outline\">\n                <Edit className=\"h-4 w-4 mr-2\" />\n                Edit Loan\n              </Button>\n            </Link>\n          )}\n          {loan.status !== 'PENDING_APPROVAL' && loan.status !== 'APPROVED' && loan.status !== 'PENDING_MORE_INFO' && (\n            <>\n              {(loan.status === 'ACTIVE' || loan.status === 'DISBURSED') && (\n                <Link href={`/payments/new?loanId=${loan.id}`}>\n                  <Button>\n                    <Plus className=\"h-4 w-4 mr-2\" />\n                    Record Payment\n                  </Button>\n                </Link>\n              )}\n            </>\n          )}\n          <Link href=\"/loans\">\n            <Button variant=\"outline\">\n              <ArrowLeft className=\"h-4 w-4 mr-2\" />\n              Back to Loans\n            </Button>\n          </Link>\n        </div>\n      }\n    >\n      <div className=\"space-y-6\">\n        {/* Loan Summary Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Principal Amount</CardTitle>\n              <DollarSign className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">LKR {Number(loan.principalAmount || 0).toLocaleString()}</div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Total Paid</CardTitle>\n              <CreditCard className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-green-600\">{formatCurrency(calculateTotalPaid())}</div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Remaining Amount</CardTitle>\n              <DollarSign className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold text-red-600\">{formatCurrency(calculateOutstanding())}</div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Status</CardTitle>\n              <Badge className={getStatusBadgeColor(loan.status)}>\n                {loan.status}\n              </Badge>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-sm text-gray-600\">\n                Interest: {loan.interestRate}% | {getTenureDisplayText()}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Detailed Information Tabs */}\n        <Tabs defaultValue=\"overview\" className=\"space-y-4\">\n          <TabsList>\n            <TabsTrigger value=\"overview\">Overview</TabsTrigger>\n            <TabsTrigger value=\"payments\">Payments ({loan.payments?.length || 0})</TabsTrigger>\n            <TabsTrigger value=\"schedule\">Payment Schedule ({loan.schedules?.length || 0})</TabsTrigger>\n            <TabsTrigger value=\"guarantors\">Guarantors ({loan.guarantors?.length || 0})</TabsTrigger>\n            <TabsTrigger value=\"documents\">Documents ({loan.documents?.length || 0})</TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"overview\" className=\"space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Customer Information */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <User className=\"h-5 w-5\" />\n                    Customer Information\n                  </CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-3\">\n                  <div>\n                    <span className=\"font-medium text-gray-600\">Name:</span>\n                    <p className=\"font-semibold\">{loan.customer.firstName} {loan.customer.lastName}</p>\n                  </div>\n                  <div>\n                    <span className=\"font-medium text-gray-600\">Phone:</span>\n                    <p className=\"font-semibold\">{loan.customer.phone}</p>\n                  </div>\n                  <div>\n                    <span className=\"font-medium text-gray-600\">Email:</span>\n                    <p className=\"font-semibold\">{loan.customer.email || 'N/A'}</p>\n                  </div>\n                  <div>\n                    <span className=\"font-medium text-gray-600\">National ID:</span>\n                    <p className=\"font-semibold\">{loan.customer.nationalId}</p>\n                  </div>\n                  <div>\n                    <span className=\"font-medium text-gray-600\">Monthly Income:</span>\n                    <p className=\"font-semibold\">LKR {Number(loan.customer.monthlyIncome || 0).toLocaleString()}</p>\n                  </div>\n                  <div>\n                    <span className=\"font-medium text-gray-600\">Employment:</span>\n                    <p className=\"font-semibold\">{loan.customer.employmentType || 'N/A'}</p>\n                  </div>\n                  <div>\n                    <span className=\"font-medium text-gray-600\">Employer:</span>\n                    <p className=\"font-semibold\">{loan.customer.employer || 'N/A'}</p>\n                  </div>\n                  <div>\n                    <span className=\"font-medium text-gray-600\">Address:</span>\n                    <p className=\"font-semibold\">{loan.customer.address || 'N/A'}</p>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Loan Information */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center gap-2\">\n                    <FileText className=\"h-5 w-5\" />\n                    Loan Information\n                  </CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-3\">\n                  <div>\n                    <span className=\"font-medium text-gray-600\">Loan Type:</span>\n                    <p className=\"font-semibold\">{loan.loanType.name}</p>\n                  </div>\n                  <div>\n                    <span className=\"font-medium text-gray-600\">Purpose:</span>\n                    <p className=\"font-semibold\">{loan.purpose || 'N/A'}</p>\n                  </div>\n                  <div>\n                    <span className=\"font-medium text-gray-600\">Repayment Frequency:</span>\n                    <p className=\"font-semibold\">{loan.repaymentFrequency}</p>\n                  </div>\n                  <div>\n                    <span className=\"font-medium text-gray-600\">Created:</span>\n                    <p className=\"font-semibold\">{new Date(loan.createdAt).toLocaleDateString()}</p>\n                  </div>\n                  <div>\n                    <span className=\"font-medium text-gray-600\">Disbursed:</span>\n                    <p className=\"font-semibold\">{loan.disbursedAt ? new Date(loan.disbursedAt).toLocaleDateString() : 'Not disbursed'}</p>\n                  </div>\n                  <div>\n                    <span className=\"font-medium text-gray-600\">Notes:</span>\n                    <p className=\"font-semibold\">{loan.notes || 'N/A'}</p>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n\n            {/* Loan Communication System */}\n            <LoanComments loanId={loan.id} />\n\n            {/* Loan Activity Log */}\n            <LoanActivityLog loanId={loan.id} />\n          </TabsContent>\n\n          <TabsContent value=\"payments\" className=\"space-y-4\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Payment History</CardTitle>\n                <CardDescription>All payments made for this loan</CardDescription>\n              </CardHeader>\n              <CardContent>\n                {loan.payments && loan.payments.length > 0 ? (\n                  <Table>\n                    <TableHeader>\n                      <TableRow>\n                        <TableHead>Date</TableHead>\n                        <TableHead>Amount</TableHead>\n                        <TableHead>Method</TableHead>\n                        <TableHead>Reference</TableHead>\n                        <TableHead>Recorded By</TableHead>\n                        <TableHead>Status</TableHead>\n                      </TableRow>\n                    </TableHeader>\n                    <TableBody>\n                      {loan.payments.map((payment) => (\n                        <TableRow key={payment.id}>\n                          <TableCell>\n                            <div>\n                              <div>{new Date(payment.paymentDate).toLocaleDateString()}</div>\n                              <div className=\"text-sm text-gray-500\">\n                                {new Date(payment.paymentDate).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n                              </div>\n                            </div>\n                          </TableCell>\n                          <TableCell className=\"font-semibold\">LKR {Number(payment.amount || 0).toLocaleString()}</TableCell>\n                          <TableCell>{payment.paymentMethod}</TableCell>\n                          <TableCell>{payment.referenceNumber || 'N/A'}</TableCell>\n                          <TableCell>\n                            <div className=\"text-sm\">\n                              {payment.createdByUser ?\n                                `${payment.createdByUser.firstName} ${payment.createdByUser.lastName}` :\n                                'System'\n                              }\n                            </div>\n                          </TableCell>\n                          <TableCell>\n                            <Badge className={getPaymentStatusColor(payment.status)}>\n                              {payment.status}\n                            </Badge>\n                          </TableCell>\n                        </TableRow>\n                      ))}\n                    </TableBody>\n                  </Table>\n                ) : (\n                  <div className=\"text-center py-8\">\n                    <p className=\"text-gray-500 mb-4\">No payments recorded yet</p>\n                    {(loan.status === 'ACTIVE' || loan.status === 'DISBURSED') ? (\n                      <Link href={`/payments/new?loanId=${loan.id}`}>\n                        <Button>\n                          <Plus className=\"h-4 w-4 mr-2\" />\n                          Record First Payment\n                        </Button>\n                      </Link>\n                    ) : (\n                      <p className=\"text-sm text-gray-400\">\n                        Payment recording is only available after loan disbursement\n                      </p>\n                    )}\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          <TabsContent value=\"schedule\" className=\"space-y-4\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Payment Schedule</CardTitle>\n                <CardDescription>\n                  Installment schedule for this loan - {loan.repaymentFrequency.toLowerCase()} payments\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                {loan.schedules && loan.schedules.length > 0 ? (\n                  <Table>\n                    <TableHeader>\n                      <TableRow>\n                        <TableHead>Installment</TableHead>\n                        <TableHead>Due Date</TableHead>\n                        <TableHead>Principal</TableHead>\n                        <TableHead>Interest</TableHead>\n                        <TableHead>Total</TableHead>\n                        <TableHead>Paid</TableHead>\n                        <TableHead>Status</TableHead>\n                      </TableRow>\n                    </TableHeader>\n                    <TableBody>\n                      {loan.schedules.map((schedule, index) => (\n                        <TableRow key={schedule.id || index}>\n                          <TableCell>#{Number(schedule.installmentNumber || 0)}</TableCell>\n                          <TableCell>{new Date(schedule.dueDate).toLocaleDateString()}</TableCell>\n                          <TableCell>LKR {Number(schedule.principalAmount || 0).toLocaleString()}</TableCell>\n                          <TableCell>LKR {Number(schedule.interestAmount || 0).toLocaleString()}</TableCell>\n                          <TableCell className=\"font-semibold\">LKR {Number(schedule.totalAmount || 0).toLocaleString()}</TableCell>\n                          <TableCell className=\"font-semibold\">LKR {Number(schedule.paidAmount || 0).toLocaleString()}</TableCell>\n                          <TableCell>\n                            <Badge className={getScheduleStatusColor(schedule.status)}>\n                              {schedule.status}\n                            </Badge>\n                          </TableCell>\n                        </TableRow>\n                      ))}\n                    </TableBody>\n                  </Table>\n                ) : (\n                  <div className=\"text-center py-8\">\n                    <p className=\"text-gray-500\">Payment schedule will be generated automatically when the loan is disbursed</p>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          <TabsContent value=\"guarantors\" className=\"space-y-4\">\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <Shield className=\"h-5 w-5\" />\n                  Guarantors\n                </CardTitle>\n                <CardDescription>People who have guaranteed this loan</CardDescription>\n              </CardHeader>\n              <CardContent>\n                {loan.guarantors && loan.guarantors.length > 0 ? (\n                  <div className=\"space-y-4\">\n                    {loan.guarantors.map((guarantor, index) => (\n                      <Card key={guarantor.id} className=\"bg-gray-50\">\n                        <CardContent className=\"pt-4\">\n                          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                            <div>\n                              <span className=\"font-medium text-gray-600\">Type:</span>\n                              <p className=\"font-semibold\">{guarantor.guarantorType}</p>\n                            </div>\n                            <div>\n                              <span className=\"font-medium text-gray-600\">Name:</span>\n                              <p className=\"font-semibold\">\n                                {guarantor.customer ?\n                                  `${guarantor.customer.firstName} ${guarantor.customer.lastName}` :\n                                  guarantor.guarantor ?\n                                  `${guarantor.guarantor.firstName} ${guarantor.guarantor.lastName}` :\n                                  'N/A'\n                                }\n                              </p>\n                            </div>\n                            <div>\n                              <span className=\"font-medium text-gray-600\">Phone:</span>\n                              <p className=\"font-semibold\">\n                                {guarantor.customer?.phone || guarantor.guarantor?.phone || 'N/A'}\n                              </p>\n                            </div>\n                            <div>\n                              <span className=\"font-medium text-gray-600\">Liability:</span>\n                              <p className=\"font-semibold\">LKR {Number(guarantor.liabilityAmount || 0).toLocaleString()}</p>\n                            </div>\n                          </div>\n                          <div className=\"mt-2\">\n                            <Badge className={guarantor.guarantorStatus === 'ACTIVE' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>\n                              {guarantor.guarantorStatus}\n                            </Badge>\n                          </div>\n\n                          {/* Show documents for individual guarantors */}\n                          {guarantor.guarantorType === 'INDIVIDUAL' && guarantor.guarantor?.documents && guarantor.guarantor.documents.length > 0 && (\n                            <div className=\"mt-4\">\n                              <h5 className=\"font-medium text-gray-700 mb-2\">Documents:</h5>\n                              <div className=\"space-y-2\">\n                                {guarantor.guarantor.documents.map((document) => (\n                                  <div key={document.id} className=\"flex items-center justify-between p-2 bg-white rounded border\">\n                                    <div>\n                                      <p className=\"font-medium text-sm\">{document.documentName}</p>\n                                      <p className=\"text-xs text-gray-500\">{document.documentType} • {new Date(document.uploadedAt).toLocaleDateString()}</p>\n                                    </div>\n                                    <div className=\"flex gap-1\">\n                                      <Button\n                                        variant=\"outline\"\n                                        size=\"sm\"\n                                        onClick={() => handleViewDocument({...document, isGuarantorDocument: true})}\n                                      >\n                                        <Eye className=\"h-3 w-3\" />\n                                      </Button>\n                                      <Button\n                                        variant=\"outline\"\n                                        size=\"sm\"\n                                        onClick={() => handleDownloadDocument({...document, isGuarantorDocument: true})}\n                                      >\n                                        <Download className=\"h-3 w-3\" />\n                                      </Button>\n                                    </div>\n                                  </div>\n                                ))}\n                              </div>\n                            </div>\n                          )}\n                        </CardContent>\n                      </Card>\n                    ))}\n                  </div>\n                ) : (\n                  <div className=\"text-center py-8\">\n                    <p className=\"text-gray-500\">No guarantors for this loan</p>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          <TabsContent value=\"documents\" className=\"space-y-4\">\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center gap-2\">\n                  <FileText className=\"h-5 w-5\" />\n                  Documents\n                </CardTitle>\n                <CardDescription>Documents uploaded for this loan</CardDescription>\n              </CardHeader>\n              <CardContent>\n                {loan.documents && loan.documents.length > 0 ? (\n                  <Table>\n                    <TableHeader>\n                      <TableRow>\n                        <TableHead>Document Name</TableHead>\n                        <TableHead>Type</TableHead>\n                        <TableHead>Uploaded</TableHead>\n                        <TableHead>Actions</TableHead>\n                      </TableRow>\n                    </TableHeader>\n                    <TableBody>\n                      {loan.documents.map((document) => (\n                        <TableRow key={document.id}>\n                          <TableCell className=\"font-semibold\">{document.originalName}</TableCell>\n                          <TableCell>{document.documentType}</TableCell>\n                          <TableCell>{new Date(document.uploadedAt).toLocaleDateString()}</TableCell>\n                          <TableCell>\n                            <div className=\"flex gap-2\">\n                              <Button\n                                variant=\"outline\"\n                                size=\"sm\"\n                                onClick={() => handleViewDocument(document)}\n                              >\n                                <Eye className=\"h-4 w-4 mr-1\" />\n                                View\n                              </Button>\n                              <Button\n                                variant=\"outline\"\n                                size=\"sm\"\n                                onClick={() => handleDownloadDocument(document)}\n                              >\n                                <Download className=\"h-4 w-4 mr-1\" />\n                                Download\n                              </Button>\n                            </div>\n                          </TableCell>\n                        </TableRow>\n                      ))}\n                    </TableBody>\n                  </Table>\n                ) : (\n                  <div className=\"text-center py-8\">\n                    <p className=\"text-gray-500\">No documents uploaded yet</p>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          </TabsContent>\n        </Tabs>\n      </div>\n\n      {/* Approval Dialog */}\n      <Dialog open={approvalDialog.isOpen} onOpenChange={(open) =>\n        setApprovalDialog(prev => ({ ...prev, isOpen: open }))\n      }>\n        <DialogContent>\n          <DialogHeader>\n            <DialogTitle>\n              {approvalDialog.action === 'APPROVE' && 'Approve Loan Application'}\n              {approvalDialog.action === 'REJECT' && 'Reject Loan Application'}\n              {approvalDialog.action === 'REQUEST_MORE_INFO' && 'Request More Information'}\n            </DialogTitle>\n            <DialogDescription>\n              {loan && (\n                <>\n                  Loan ID: {loan.loanNumber} - {loan.customer.firstName} {loan.customer.lastName}\n                  <br />\n                  Amount: LKR {Number(loan.principalAmount).toLocaleString()}\n                </>\n              )}\n            </DialogDescription>\n          </DialogHeader>\n\n          <div className=\"space-y-4\">\n            <div>\n              <Label htmlFor=\"notes\">Notes</Label>\n              <Textarea\n                id=\"notes\"\n                value={notes}\n                onChange={(e) => setNotes(e.target.value)}\n                placeholder=\"Add any notes or comments...\"\n                rows={3}\n              />\n            </div>\n\n            {approvalDialog.action === 'REQUEST_MORE_INFO' && (\n              <div>\n                <Label htmlFor=\"requestedInfo\">Requested Information *</Label>\n                <Textarea\n                  id=\"requestedInfo\"\n                  value={requestedInfo}\n                  onChange={(e) => setRequestedInfo(e.target.value)}\n                  placeholder=\"Specify what additional information is needed...\"\n                  rows={3}\n                  required\n                />\n              </div>\n            )}\n          </div>\n\n          <DialogFooter>\n            <Button\n              variant=\"outline\"\n              onClick={() => setApprovalDialog({ isOpen: false, action: null })}\n            >\n              Cancel\n            </Button>\n            <Button\n              variant={getActionButtonProps(approvalDialog.action || '').variant}\n              onClick={processApproval}\n              disabled={processing}\n            >\n              {processing ? 'Processing...' : `Confirm ${approvalDialog.action?.replace('_', ' ')}`}\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* Disbursement Dialog */}\n      <Dialog open={disbursementDialog} onOpenChange={setDisbursementDialog}>\n        <DialogContent className=\"max-w-md\">\n          <DialogHeader>\n            <DialogTitle>Disburse Loan</DialogTitle>\n            <DialogDescription>\n              {loan && (\n                <>\n                  Loan ID: {loan.loanNumber} - {loan.customer.firstName} {loan.customer.lastName}\n                  <br />\n                  Principal Amount: LKR {Number(loan.principalAmount).toLocaleString()}\n                </>\n              )}\n            </DialogDescription>\n          </DialogHeader>\n\n          <div className=\"space-y-4\">\n            <div>\n              <Label htmlFor=\"disbursedAmount\">Disbursed Amount *</Label>\n              <Input\n                id=\"disbursedAmount\"\n                type=\"number\"\n                value={disbursementData.disbursedAmount}\n                onChange={(e) => setDisbursementData(prev => ({\n                  ...prev,\n                  disbursedAmount: parseFloat(e.target.value) || 0\n                }))}\n                placeholder=\"Enter disbursed amount\"\n                required\n              />\n            </div>\n\n            <div>\n              <Label htmlFor=\"disbursementMethod\">Disbursement Method *</Label>\n              <Select\n                value={disbursementData.disbursementMethod}\n                onValueChange={handleDisbursementMethodChange}\n              >\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Select disbursement method\" />\n                </SelectTrigger>\n                <SelectContent>\n                  <SelectItem value=\"CASH\">Cash</SelectItem>\n                  <SelectItem value=\"BANK_TRANSFER\">Bank Transfer</SelectItem>\n                  <SelectItem value=\"CHEQUE\">Cheque</SelectItem>\n                  <SelectItem value=\"ONLINE\">Online Transfer</SelectItem>\n                  <SelectItem value=\"MOBILE_PAYMENT\">Mobile Payment</SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n\n            <div>\n              <Label htmlFor=\"disbursementReference\">Reference Number</Label>\n              <Input\n                id=\"disbursementReference\"\n                value={disbursementData.disbursementReference}\n                onChange={(e) => setDisbursementData(prev => ({\n                  ...prev,\n                  disbursementReference: e.target.value\n                }))}\n                placeholder=\"Transaction/Reference number\"\n              />\n            </div>\n\n            <div>\n              <Label htmlFor=\"disbursementDate\">Disbursement Date *</Label>\n              <Input\n                id=\"disbursementDate\"\n                type=\"date\"\n                value={disbursementData.disbursementDate}\n                onChange={(e) => setDisbursementData(prev => ({\n                  ...prev,\n                  disbursementDate: e.target.value\n                }))}\n                required\n              />\n            </div>\n\n            <div>\n              <Label htmlFor=\"disbursementNotes\">Notes</Label>\n              <Textarea\n                id=\"disbursementNotes\"\n                value={disbursementData.disbursementNotes}\n                onChange={(e) => setDisbursementData(prev => ({\n                  ...prev,\n                  disbursementNotes: e.target.value\n                }))}\n                placeholder=\"Add any notes about the disbursement...\"\n                rows={3}\n              />\n            </div>\n          </div>\n\n          <DialogFooter>\n            <Button\n              variant=\"outline\"\n              onClick={() => setDisbursementDialog(false)}\n            >\n              Cancel\n            </Button>\n            <Button\n              onClick={processDisbursement}\n              disabled={disbursing || !disbursementData.disbursedAmount || !disbursementData.disbursementMethod}\n            >\n              {disbursing ? 'Disbursing...' : 'Disburse Loan'}\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    </PageHeader>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAiBA;AACA;AACA;;;AApDA;;;;;;;;;;;;;;;;;;;;;;AAkJe,SAAS;QAwf6B,gBACQ,iBACJ,kBACF,iBA4aE;;IAt6BvD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACrD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,wBAAwB;IACxB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGhD;QACD,QAAQ;QACR,QAAQ;IACV;IACA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,4BAA4B;IAC5B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvD,iBAAiB;QACjB,oBAAoB;QACpB,uBAAuB;QACvB,mBAAmB;QACnB,kBAAkB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC1D;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,OAAO,EAAE,EAAE;gBACb,iBAAiB,OAAO,EAAE;YAC5B;QACF;oCAAG;QAAC,OAAO,EAAE;KAAC;IAEd,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM,AAAC,cAAoB,OAAP;YAC3C,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,oCAAoC;gBACpC,MAAM,mBAAmB,MAAM,MAAM,AAAC,cAAoB,OAAP,QAAO;gBAC1D,IAAI,iBAAiB,EAAE,EAAE;oBACvB,MAAM,eAAe,MAAM,iBAAiB,IAAI;oBAChD,KAAK,SAAS,GAAG;gBACnB;gBAEA,QAAQ;YACV,OAAO;gBACL,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,WAAW;QACb;IACF;IAIA,MAAM,uBAAuB,CAAC;QAC5B,kBAAkB;YAChB,QAAQ;YACR;QACF;QACA,SAAS;QACT,iBAAiB;IACnB;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,QAAQ,CAAC,eAAe,MAAM,EAAE;QAErC,IAAI;YACF,cAAc;YACd,MAAM,WAAW,MAAM,MAAM,AAAC,cAAqB,OAAR,KAAK,EAAE,EAAC,aAAW;gBAC5D,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBACnB,QAAQ,eAAe,MAAM;oBAC7B;oBACA,GAAI,eAAe,MAAM,KAAK,uBAAuB;wBAAE;oBAAc,CAAC;gBACxE;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM;oBACJ,OAAO;oBACP,aAAa,KAAK,OAAO;gBAC3B;gBAEA,8CAA8C;gBAC9C,MAAM,iBAAiB,KAAK,EAAE;gBAC9B,kBAAkB;oBAAE,QAAQ;oBAAO,QAAQ;gBAAK;YAClD,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM;oBACJ,OAAO;oBACP,aAAa,MAAM,KAAK,IAAI;oBAC5B,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO;oBAAE,SAAS;oBAAoB,MAAM;gBAAU;YACxD,KAAK;gBACH,OAAO;oBAAE,SAAS;oBAAwB,MAAM;gBAAS;YAC3D,KAAK;gBACH,OAAO;oBAAE,SAAS;oBAAoB,MAAM;gBAAe;YAC7D;gBACE,OAAO;oBAAE,SAAS;oBAAoB,MAAM;gBAAU;QAC1D;IACF;IAEA,yDAAyD;IACzD,MAAM,gCAAgC,CAAC;QACrC,MAAM,OAAO,IAAI;QACjB,MAAM,OAAO,KAAK,WAAW,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;QAClD,MAAM,QAAQ,CAAC,KAAK,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;QAC3D,MAAM,MAAM,KAAK,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;QAClD,MAAM,OAAO,KAAK,OAAO,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;QAE9C,MAAM,WAAW;YACf,QAAQ;YACR,iBAAiB;YACjB,UAAU;YACV,UAAU;YACV,kBAAkB;QACpB;QAEA,MAAM,SAAS,QAAQ,CAAC,OAAgC,IAAI;QAC5D,OAAO,AAAC,GAAW,OAAT,QAAgB,OAAP,MAAe,OAAR,OAAc,OAAN,KAAW,OAAL;IAC1C;IAEA,MAAM,qBAAqB;QACzB,IAAI,MAAM;YACR,oBAAoB,CAAA,OAAQ,CAAC;oBAC3B,GAAG,IAAI;oBACP,iBAAiB,OAAO,KAAK,eAAe;gBAC9C,CAAC;YACD,sBAAsB;QACxB;IACF;IAEA,gEAAgE;IAChE,MAAM,iCAAiC,CAAC;QACtC,MAAM,eAAe,8BAA8B;QACnD,oBAAoB,CAAA,OAAQ,CAAC;gBAC3B,GAAG,IAAI;gBACP,oBAAoB;gBACpB,uBAAuB;YACzB,CAAC;IACH;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,MAAM;QAEX,IAAI;YACF,cAAc;YACd,MAAM,WAAW,MAAM,MAAM,AAAC,cAAqB,OAAR,KAAK,EAAE,EAAC,cAAY;gBAC7D,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM;oBACJ,OAAO;oBACP,aAAa,KAAK,OAAO;gBAC3B;gBAEA,8CAA8C;gBAC9C,MAAM,iBAAiB,KAAK,EAAE;gBAC9B,sBAAsB;YACxB,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM;oBACJ,OAAO;oBACP,aAAa,MAAM,KAAK,IAAI;oBAC5B,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,EAAC,iBAAA,2BAAA,KAAM,OAAO,GAAE,OAAO;QAC3B,OAAO,KAAK,OAAO,CAAC,SAAS;IAC/B;IAEA,MAAM,uBAAuB;QAC3B,IAAI,EAAC,iBAAA,2BAAA,KAAM,OAAO,GAAE,OAAO;QAC3B,OAAO,KAAK,OAAO,CAAC,iBAAiB;IACvC;IAEA,8DAA8D;IAC9D,MAAM,wBAAwB,CAAC,MAAc;QAC3C,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAS,OAAO,KAAK,KAAK,CAAC,OAAO;YACvC,KAAK;gBAAU,OAAO,KAAK,KAAK,CAAC,OAAO;YACxC,KAAK;gBAAS,OAAO,KAAK,KAAK,CAAC,OAAO;YACvC;gBAAS,OAAO;QAClB;IACF;IAEA,6CAA6C;IAC7C,MAAM,uBAAuB;YACtB;QAAL,IAAI,EAAC,iBAAA,4BAAA,iBAAA,KAAM,QAAQ,cAAd,qCAAA,eAAgB,UAAU,GAAE,OAAO,AAAC,GAAe,OAAb,iBAAA,2BAAA,KAAM,MAAM,EAAC;QAExD,MAAM,kBAAkB,sBAAsB,OAAO,KAAK,MAAM,GAAG,KAAK,QAAQ,CAAC,UAAU;QAC3F,MAAM,OAAO,KAAK,QAAQ,CAAC,UAAU,CAAC,WAAW;QACjD,OAAO,AAAC,GAAqB,OAAnB,iBAAgB,KAAQ,OAAL;IAC/B;IAEA,MAAM,qBAAqB,CAAC;QAC1B,4EAA4E;QAC5E,IAAI,SAAS,UAAU,EAAE;YACvB,OAAO,IAAI,CAAC,SAAS,UAAU,EAAE;QACnC,OAAO;YACL,yDAAyD;YACzD,MAAM,WAAW,SAAS,mBAAmB,GACzC,AAAC,4BAAuC,OAAZ,SAAS,EAAE,EAAC,WACxC,AAAC,kBAA6B,OAAZ,SAAS,EAAE,EAAC;YAClC,OAAO,IAAI,CAAC,UAAU;QACxB;IACF;IAEA,MAAM,yBAAyB,OAAO;QACpC,IAAI;YACF,MAAM,WAAW,SAAS,mBAAmB,GACzC,AAAC,4BAAuC,OAAZ,SAAS,EAAE,EAAC,eACxC,AAAC,kBAA6B,OAAZ,SAAS,EAAE,EAAC;YAClC,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;gBACvC,MAAM,IAAI,SAAS,aAAa,CAAC;gBACjC,EAAE,KAAK,CAAC,OAAO,GAAG;gBAClB,EAAE,IAAI,GAAG;gBACT,EAAE,QAAQ,GAAG,SAAS,YAAY,IAAI,SAAS,QAAQ;gBACvD,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,EAAE,KAAK;gBACP,OAAO,GAAG,CAAC,eAAe,CAAC;gBAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B,OAAO;gBACL,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC,6IAAA,CAAA,UAAU;YAAC,OAAM;YAAa,aAAY;sBACzC,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;IAIvB;IAEA,IAAI,CAAC,MAAM;QACT,qBACE,6LAAC,6IAAA,CAAA,UAAU;YAAC,OAAM;YAAiB,aAAY;sBAC7C,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;;8CACL,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;IAOlD;IAEA,qBACE,6LAAC,6IAAA,CAAA,UAAU;QACT,OAAO,AAAC,QAAuB,OAAhB,KAAK,UAAU;QAC9B,aAAa,AAAC,GAA6B,OAA3B,KAAK,QAAQ,CAAC,SAAS,EAAC,KAA+B,OAA5B,KAAK,QAAQ,CAAC,QAAQ,EAAC,OAAwB,OAAnB,KAAK,QAAQ,CAAC,IAAI;QACzF,uBACE,6LAAC;YAAI,WAAU;;gBACZ,KAAK,MAAM,KAAK,sBAAsB,cAAc,kCACnD;;sCACE,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,qBAAqB;;8CAEpC,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAG1C,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,qBAAqB;;8CAEpC,6LAAC,+MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAGtC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,qBAAqB;;8CAEpC,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;gBAK7C,KAAK,MAAM,KAAK,uBAAuB,cAAc,kCACpD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC;oCAAK,WAAU;8CAAsC;;;;;;;;;;;;sCAIxD,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,qBAAqB;;8CAEpC,6LAAC,8NAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAG1C,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,qBAAqB;;8CAEpC,6LAAC,+MAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;gBAKzC,KAAK,MAAM,KAAK,4BACf,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAQ;oBACR,SAAS;;sCAET,6LAAC,6MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;gBAIxC,KAAK,MAAM,KAAK,qCACf,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAM,AAAC,UAAiB,OAAR,KAAK,EAAE,EAAC;8BAC5B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;;0CACd,6LAAC,8MAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;gBAKtC,KAAK,MAAM,KAAK,sBAAsB,KAAK,MAAM,KAAK,cAAc,KAAK,MAAM,KAAK,qCACnF;8BACG,CAAC,KAAK,MAAM,KAAK,YAAY,KAAK,MAAM,KAAK,WAAW,mBACvD,6LAAC,+JAAA,CAAA,UAAI;wBAAC,MAAM,AAAC,wBAA+B,OAAR,KAAK,EAAE;kCACzC,cAAA,6LAAC,qIAAA,CAAA,SAAM;;8CACL,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;8BAO3C,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;8BACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;;0CACd,6LAAC,mNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;0BAO9C,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;gDAAqB;gDAAK,OAAO,KAAK,eAAe,IAAI,GAAG,cAAc;;;;;;;;;;;;;;;;;;0CAI7F,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;sDAAqC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;;;;;0CAIvE,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;sDAAmC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;;;;;0CAIrE,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,6LAAC,oIAAA,CAAA,QAAK;gDAAC,WAAW,oBAAoB,KAAK,MAAM;0DAC9C,KAAK,MAAM;;;;;;;;;;;;kDAGhB,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;gDAAwB;gDAC1B,KAAK,YAAY;gDAAC;gDAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAO1C,6LAAC,mIAAA,CAAA,OAAI;wBAAC,cAAa;wBAAW,WAAU;;0CACtC,6LAAC,mIAAA,CAAA,WAAQ;;kDACP,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;kDAAW;;;;;;kDAC9B,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;;4CAAW;4CAAW,EAAA,iBAAA,KAAK,QAAQ,cAAb,qCAAA,eAAe,MAAM,KAAI;4CAAE;;;;;;;kDACpE,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;;4CAAW;4CAAmB,EAAA,kBAAA,KAAK,SAAS,cAAd,sCAAA,gBAAgB,MAAM,KAAI;4CAAE;;;;;;;kDAC7E,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;;4CAAa;4CAAa,EAAA,mBAAA,KAAK,UAAU,cAAf,uCAAA,iBAAiB,MAAM,KAAI;4CAAE;;;;;;;kDAC1E,6LAAC,mIAAA,CAAA,cAAW;wCAAC,OAAM;;4CAAY;4CAAY,EAAA,kBAAA,KAAK,SAAS,cAAd,sCAAA,gBAAgB,MAAM,KAAI;4CAAE;;;;;;;;;;;;;0CAGzE,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;;kDACtC,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC,mIAAA,CAAA,OAAI;;kEACH,6LAAC,mIAAA,CAAA,aAAU;kEACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;kEAIhC,6LAAC,mIAAA,CAAA,cAAW;wDAAC,WAAU;;0EACrB,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;kFAC5C,6LAAC;wEAAE,WAAU;;4EAAiB,KAAK,QAAQ,CAAC,SAAS;4EAAC;4EAAE,KAAK,QAAQ,CAAC,QAAQ;;;;;;;;;;;;;0EAEhF,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;kFAC5C,6LAAC;wEAAE,WAAU;kFAAiB,KAAK,QAAQ,CAAC,KAAK;;;;;;;;;;;;0EAEnD,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;kFAC5C,6LAAC;wEAAE,WAAU;kFAAiB,KAAK,QAAQ,CAAC,KAAK,IAAI;;;;;;;;;;;;0EAEvD,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;kFAC5C,6LAAC;wEAAE,WAAU;kFAAiB,KAAK,QAAQ,CAAC,UAAU;;;;;;;;;;;;0EAExD,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;kFAC5C,6LAAC;wEAAE,WAAU;;4EAAgB;4EAAK,OAAO,KAAK,QAAQ,CAAC,aAAa,IAAI,GAAG,cAAc;;;;;;;;;;;;;0EAE3F,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;kFAC5C,6LAAC;wEAAE,WAAU;kFAAiB,KAAK,QAAQ,CAAC,cAAc,IAAI;;;;;;;;;;;;0EAEhE,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;kFAC5C,6LAAC;wEAAE,WAAU;kFAAiB,KAAK,QAAQ,CAAC,QAAQ,IAAI;;;;;;;;;;;;0EAE1D,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;kFAC5C,6LAAC;wEAAE,WAAU;kFAAiB,KAAK,QAAQ,CAAC,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;0DAM7D,6LAAC,mIAAA,CAAA,OAAI;;kEACH,6LAAC,mIAAA,CAAA,aAAU;kEACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;;8EACnB,6LAAC,iNAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;kEAIpC,6LAAC,mIAAA,CAAA,cAAW;wDAAC,WAAU;;0EACrB,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;kFAC5C,6LAAC;wEAAE,WAAU;kFAAiB,KAAK,QAAQ,CAAC,IAAI;;;;;;;;;;;;0EAElD,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;kFAC5C,6LAAC;wEAAE,WAAU;kFAAiB,KAAK,OAAO,IAAI;;;;;;;;;;;;0EAEhD,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;kFAC5C,6LAAC;wEAAE,WAAU;kFAAiB,KAAK,kBAAkB;;;;;;;;;;;;0EAEvD,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;kFAC5C,6LAAC;wEAAE,WAAU;kFAAiB,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;;;;;;0EAE3E,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;kFAC5C,6LAAC;wEAAE,WAAU;kFAAiB,KAAK,WAAW,GAAG,IAAI,KAAK,KAAK,WAAW,EAAE,kBAAkB,KAAK;;;;;;;;;;;;0EAErG,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAA4B;;;;;;kFAC5C,6LAAC;wEAAE,WAAU;kFAAiB,KAAK,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOpD,6LAAC,8IAAA,CAAA,UAAY;wCAAC,QAAQ,KAAK,EAAE;;;;;;kDAG7B,6LAAC,gJAAA,CAAA,UAAe;wCAAC,QAAQ,KAAK,EAAE;;;;;;;;;;;;0CAGlC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;0CACtC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAEnB,6LAAC,mIAAA,CAAA,cAAW;sDACT,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,kBACvC,6LAAC,oIAAA,CAAA,QAAK;;kEACJ,6LAAC,oIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;8EACP,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;;;;;;;;;;;;kEAGf,6LAAC,oIAAA,CAAA,YAAS;kEACP,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAClB,6LAAC,oIAAA,CAAA,WAAQ;;kFACP,6LAAC,oIAAA,CAAA,YAAS;kFACR,cAAA,6LAAC;;8FACC,6LAAC;8FAAK,IAAI,KAAK,QAAQ,WAAW,EAAE,kBAAkB;;;;;;8FACtD,6LAAC;oFAAI,WAAU;8FACZ,IAAI,KAAK,QAAQ,WAAW,EAAE,kBAAkB,CAAC,EAAE,EAAE;wFAAE,MAAM;wFAAW,QAAQ;oFAAU;;;;;;;;;;;;;;;;;kFAIjG,6LAAC,oIAAA,CAAA,YAAS;wEAAC,WAAU;;4EAAgB;4EAAK,OAAO,QAAQ,MAAM,IAAI,GAAG,cAAc;;;;;;;kFACpF,6LAAC,oIAAA,CAAA,YAAS;kFAAE,QAAQ,aAAa;;;;;;kFACjC,6LAAC,oIAAA,CAAA,YAAS;kFAAE,QAAQ,eAAe,IAAI;;;;;;kFACvC,6LAAC,oIAAA,CAAA,YAAS;kFACR,cAAA,6LAAC;4EAAI,WAAU;sFACZ,QAAQ,aAAa,GACpB,AAAC,GAAqC,OAAnC,QAAQ,aAAa,CAAC,SAAS,EAAC,KAAkC,OAA/B,QAAQ,aAAa,CAAC,QAAQ,IACpE;;;;;;;;;;;kFAIN,6LAAC,oIAAA,CAAA,YAAS;kFACR,cAAA,6LAAC,oIAAA,CAAA,QAAK;4EAAC,WAAW,sBAAsB,QAAQ,MAAM;sFACnD,QAAQ,MAAM;;;;;;;;;;;;+DAtBN,QAAQ,EAAE;;;;;;;;;;;;;;;qEA8B/B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAqB;;;;;;oDAChC,KAAK,MAAM,KAAK,YAAY,KAAK,MAAM,KAAK,4BAC5C,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAM,AAAC,wBAA+B,OAAR,KAAK,EAAE;kEACzC,cAAA,6LAAC,qIAAA,CAAA,SAAM;;8EACL,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;6EAKrC,6LAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAUjD,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;0CACtC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,mIAAA,CAAA,kBAAe;;wDAAC;wDACuB,KAAK,kBAAkB,CAAC,WAAW;wDAAG;;;;;;;;;;;;;sDAGhF,6LAAC,mIAAA,CAAA,cAAW;sDACT,KAAK,SAAS,IAAI,KAAK,SAAS,CAAC,MAAM,GAAG,kBACzC,6LAAC,oIAAA,CAAA,QAAK;;kEACJ,6LAAC,oIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;8EACP,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;;;;;;;;;;;;kEAGf,6LAAC,oIAAA,CAAA,YAAS;kEACP,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,sBAC7B,6LAAC,oIAAA,CAAA,WAAQ;;kFACP,6LAAC,oIAAA,CAAA,YAAS;;4EAAC;4EAAE,OAAO,SAAS,iBAAiB,IAAI;;;;;;;kFAClD,6LAAC,oIAAA,CAAA,YAAS;kFAAE,IAAI,KAAK,SAAS,OAAO,EAAE,kBAAkB;;;;;;kFACzD,6LAAC,oIAAA,CAAA,YAAS;;4EAAC;4EAAK,OAAO,SAAS,eAAe,IAAI,GAAG,cAAc;;;;;;;kFACpE,6LAAC,oIAAA,CAAA,YAAS;;4EAAC;4EAAK,OAAO,SAAS,cAAc,IAAI,GAAG,cAAc;;;;;;;kFACnE,6LAAC,oIAAA,CAAA,YAAS;wEAAC,WAAU;;4EAAgB;4EAAK,OAAO,SAAS,WAAW,IAAI,GAAG,cAAc;;;;;;;kFAC1F,6LAAC,oIAAA,CAAA,YAAS;wEAAC,WAAU;;4EAAgB;4EAAK,OAAO,SAAS,UAAU,IAAI,GAAG,cAAc;;;;;;;kFACzF,6LAAC,oIAAA,CAAA,YAAS;kFACR,cAAA,6LAAC,oIAAA,CAAA,QAAK;4EAAC,WAAW,uBAAuB,SAAS,MAAM;sFACrD,SAAS,MAAM;;;;;;;;;;;;+DATP,SAAS,EAAE,IAAI;;;;;;;;;;;;;;;qEAiBpC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOvC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAa,WAAU;0CACxC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,yMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAY;;;;;;;8DAGhC,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAEnB,6LAAC,mIAAA,CAAA,cAAW;sDACT,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,MAAM,GAAG,kBAC3C,6LAAC;gDAAI,WAAU;0DACZ,KAAK,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW;wDAsBpB,qBAA6B,sBAeS;yEApCjD,6LAAC,mIAAA,CAAA,OAAI;wDAAoB,WAAU;kEACjC,cAAA,6LAAC,mIAAA,CAAA,cAAW;4DAAC,WAAU;;8EACrB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;8FACC,6LAAC;oFAAK,WAAU;8FAA4B;;;;;;8FAC5C,6LAAC;oFAAE,WAAU;8FAAiB,UAAU,aAAa;;;;;;;;;;;;sFAEvD,6LAAC;;8FACC,6LAAC;oFAAK,WAAU;8FAA4B;;;;;;8FAC5C,6LAAC;oFAAE,WAAU;8FACV,UAAU,QAAQ,GACjB,AAAC,GAAkC,OAAhC,UAAU,QAAQ,CAAC,SAAS,EAAC,KAA+B,OAA5B,UAAU,QAAQ,CAAC,QAAQ,IAC9D,UAAU,SAAS,GACnB,AAAC,GAAmC,OAAjC,UAAU,SAAS,CAAC,SAAS,EAAC,KAAgC,OAA7B,UAAU,SAAS,CAAC,QAAQ,IAChE;;;;;;;;;;;;sFAIN,6LAAC;;8FACC,6LAAC;oFAAK,WAAU;8FAA4B;;;;;;8FAC5C,6LAAC;oFAAE,WAAU;8FACV,EAAA,sBAAA,UAAU,QAAQ,cAAlB,0CAAA,oBAAoB,KAAK,OAAI,uBAAA,UAAU,SAAS,cAAnB,2CAAA,qBAAqB,KAAK,KAAI;;;;;;;;;;;;sFAGhE,6LAAC;;8FACC,6LAAC;oFAAK,WAAU;8FAA4B;;;;;;8FAC5C,6LAAC;oFAAE,WAAU;;wFAAgB;wFAAK,OAAO,UAAU,eAAe,IAAI,GAAG,cAAc;;;;;;;;;;;;;;;;;;;8EAG3F,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;wEAAC,WAAW,UAAU,eAAe,KAAK,WAAW,gCAAgC;kFACxF,UAAU,eAAe;;;;;;;;;;;gEAK7B,UAAU,aAAa,KAAK,kBAAgB,wBAAA,UAAU,SAAS,cAAnB,4CAAA,sBAAqB,SAAS,KAAI,UAAU,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,mBACpH,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAAiC;;;;;;sFAC/C,6LAAC;4EAAI,WAAU;sFACZ,UAAU,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,yBAClC,6LAAC;oFAAsB,WAAU;;sGAC/B,6LAAC;;8GACC,6LAAC;oGAAE,WAAU;8GAAuB,SAAS,YAAY;;;;;;8GACzD,6LAAC;oGAAE,WAAU;;wGAAyB,SAAS,YAAY;wGAAC;wGAAI,IAAI,KAAK,SAAS,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;sGAElH,6LAAC;4FAAI,WAAU;;8GACb,6LAAC,qIAAA,CAAA,SAAM;oGACL,SAAQ;oGACR,MAAK;oGACL,SAAS,IAAM,mBAAmB;4GAAC,GAAG,QAAQ;4GAAE,qBAAqB;wGAAI;8GAEzE,cAAA,6LAAC,mMAAA,CAAA,MAAG;wGAAC,WAAU;;;;;;;;;;;8GAEjB,6LAAC,qIAAA,CAAA,SAAM;oGACL,SAAQ;oGACR,MAAK;oGACL,SAAS,IAAM,uBAAuB;4GAAC,GAAG,QAAQ;4GAAE,qBAAqB;wGAAI;8GAE7E,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wGAAC,WAAU;;;;;;;;;;;;;;;;;;mFAlBhB,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;uDAzCtB,UAAU,EAAE;;;;;;;;;;qEAwE3B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOvC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAY,WAAU;0CACvC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC,iNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAY;;;;;;;8DAGlC,6LAAC,mIAAA,CAAA,kBAAe;8DAAC;;;;;;;;;;;;sDAEnB,6LAAC,mIAAA,CAAA,cAAW;sDACT,KAAK,SAAS,IAAI,KAAK,SAAS,CAAC,MAAM,GAAG,kBACzC,6LAAC,oIAAA,CAAA,QAAK;;kEACJ,6LAAC,oIAAA,CAAA,cAAW;kEACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;8EACP,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;8EACX,6LAAC,oIAAA,CAAA,YAAS;8EAAC;;;;;;;;;;;;;;;;;kEAGf,6LAAC,oIAAA,CAAA,YAAS;kEACP,KAAK,SAAS,CAAC,GAAG,CAAC,CAAC,yBACnB,6LAAC,oIAAA,CAAA,WAAQ;;kFACP,6LAAC,oIAAA,CAAA,YAAS;wEAAC,WAAU;kFAAiB,SAAS,YAAY;;;;;;kFAC3D,6LAAC,oIAAA,CAAA,YAAS;kFAAE,SAAS,YAAY;;;;;;kFACjC,6LAAC,oIAAA,CAAA,YAAS;kFAAE,IAAI,KAAK,SAAS,UAAU,EAAE,kBAAkB;;;;;;kFAC5D,6LAAC,oIAAA,CAAA,YAAS;kFACR,cAAA,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,qIAAA,CAAA,SAAM;oFACL,SAAQ;oFACR,MAAK;oFACL,SAAS,IAAM,mBAAmB;;sGAElC,6LAAC,mMAAA,CAAA,MAAG;4FAAC,WAAU;;;;;;wFAAiB;;;;;;;8FAGlC,6LAAC,qIAAA,CAAA,SAAM;oFACL,SAAQ;oFACR,MAAK;oFACL,SAAS,IAAM,uBAAuB;;sGAEtC,6LAAC,6MAAA,CAAA,WAAQ;4FAAC,WAAU;;;;;;wFAAiB;;;;;;;;;;;;;;;;;;;+DAnB9B,SAAS,EAAE;;;;;;;;;;;;;;;qEA6BhC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU3C,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM,eAAe,MAAM;gBAAE,cAAc,CAAC,OAClD,kBAAkB,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,QAAQ;wBAAK,CAAC;0BAEpD,cAAA,6LAAC,qIAAA,CAAA,gBAAa;;sCACZ,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,cAAW;;wCACT,eAAe,MAAM,KAAK,aAAa;wCACvC,eAAe,MAAM,KAAK,YAAY;wCACtC,eAAe,MAAM,KAAK,uBAAuB;;;;;;;8CAEpD,6LAAC,qIAAA,CAAA,oBAAiB;8CACf,sBACC;;4CAAE;4CACU,KAAK,UAAU;4CAAC;4CAAI,KAAK,QAAQ,CAAC,SAAS;4CAAC;4CAAE,KAAK,QAAQ,CAAC,QAAQ;0DAC9E,6LAAC;;;;;4CAAK;4CACO,OAAO,KAAK,eAAe,EAAE,cAAc;;;;;;;;;;;;;;sCAMhE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,6LAAC,uIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,aAAY;4CACZ,MAAM;;;;;;;;;;;;gCAIT,eAAe,MAAM,KAAK,qCACzB,6LAAC;;sDACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAgB;;;;;;sDAC/B,6LAAC,uIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;4CAChD,aAAY;4CACZ,MAAM;4CACN,QAAQ;;;;;;;;;;;;;;;;;;sCAMhB,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,kBAAkB;4CAAE,QAAQ;4CAAO,QAAQ;wCAAK;8CAChE;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,qBAAqB,eAAe,MAAM,IAAI,IAAI,OAAO;oCAClE,SAAS;oCACT,UAAU;8CAET,aAAa,kBAAkB,AAAC,WAAmD,QAAzC,yBAAA,eAAe,MAAM,cAArB,6CAAA,uBAAuB,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;0BAOvF,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAoB,cAAc;0BAC9C,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,6LAAC,qIAAA,CAAA,oBAAiB;8CACf,sBACC;;4CAAE;4CACU,KAAK,UAAU;4CAAC;4CAAI,KAAK,QAAQ,CAAC,SAAS;4CAAC;4CAAE,KAAK,QAAQ,CAAC,QAAQ;0DAC9E,6LAAC;;;;;4CAAK;4CACiB,OAAO,KAAK,eAAe,EAAE,cAAc;;;;;;;;;;;;;;sCAM1E,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAkB;;;;;;sDACjC,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,OAAO,iBAAiB,eAAe;4CACvC,UAAU,CAAC,IAAM,oBAAoB,CAAA,OAAQ,CAAC;wDAC5C,GAAG,IAAI;wDACP,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;oDACjD,CAAC;4CACD,aAAY;4CACZ,QAAQ;;;;;;;;;;;;8CAIZ,6LAAC;;sDACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAqB;;;;;;sDACpC,6LAAC,qIAAA,CAAA,SAAM;4CACL,OAAO,iBAAiB,kBAAkB;4CAC1C,eAAe;;8DAEf,6LAAC,qIAAA,CAAA,gBAAa;8DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,gBAAa;;sEACZ,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAO;;;;;;sEACzB,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAgB;;;;;;sEAClC,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS;;;;;;sEAC3B,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAS;;;;;;sEAC3B,6LAAC,qIAAA,CAAA,aAAU;4DAAC,OAAM;sEAAiB;;;;;;;;;;;;;;;;;;;;;;;;8CAKzC,6LAAC;;sDACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAwB;;;;;;sDACvC,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,iBAAiB,qBAAqB;4CAC7C,UAAU,CAAC,IAAM,oBAAoB,CAAA,OAAQ,CAAC;wDAC5C,GAAG,IAAI;wDACP,uBAAuB,EAAE,MAAM,CAAC,KAAK;oDACvC,CAAC;4CACD,aAAY;;;;;;;;;;;;8CAIhB,6LAAC;;sDACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAmB;;;;;;sDAClC,6LAAC,oIAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,OAAO,iBAAiB,gBAAgB;4CACxC,UAAU,CAAC,IAAM,oBAAoB,CAAA,OAAQ,CAAC;wDAC5C,GAAG,IAAI;wDACP,kBAAkB,EAAE,MAAM,CAAC,KAAK;oDAClC,CAAC;4CACD,QAAQ;;;;;;;;;;;;8CAIZ,6LAAC;;sDACC,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAoB;;;;;;sDACnC,6LAAC,uIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,OAAO,iBAAiB,iBAAiB;4CACzC,UAAU,CAAC,IAAM,oBAAoB,CAAA,OAAQ,CAAC;wDAC5C,GAAG,IAAI;wDACP,mBAAmB,EAAE,MAAM,CAAC,KAAK;oDACnC,CAAC;4CACD,aAAY;4CACZ,MAAM;;;;;;;;;;;;;;;;;;sCAKZ,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,sBAAsB;8CACtC;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,cAAc,CAAC,iBAAiB,eAAe,IAAI,CAAC,iBAAiB,kBAAkB;8CAEhG,aAAa,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9C;GA5hCwB;;QACP,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;QACE,iJAAA,CAAA,aAAU;QAClB,+HAAA,CAAA,WAAQ;QACA,iIAAA,CAAA,iBAAc;;;KALlB", "debugId": null}}]}