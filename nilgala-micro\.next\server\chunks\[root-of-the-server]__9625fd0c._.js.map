{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Cred<PERSON><PERSON><PERSON>rovider from 'next-auth/providers/credentials'\nimport { PrismaAdapter } from '@auth/prisma-adapter'\nimport { prisma } from './prisma'\nimport bcrypt from 'bcryptjs'\nimport { UserRole } from '@prisma/client'\n\n// Cache for role permissions to avoid database calls on every request\nlet rolePermissionsCache: Record<string, string[]> = {}\nlet cacheLastUpdated = 0\nconst CACHE_DURATION = 5 * 60 * 1000 // 5 minutes\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma) as any,\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user || !user.isActive) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        // Update last login\n        await prisma.user.update({\n          where: { id: user.id },\n          data: { lastLogin: new Date() }\n        })\n\n        return {\n          id: user.id,\n          email: user.email,\n          firstName: user.firstName,\n          lastName: user.lastName,\n          role: user.role,\n          avatar: user.avatar || undefined,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n        token.firstName = user.firstName\n        token.lastName = user.lastName\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as UserRole\n        session.user.firstName = token.firstName as string\n        session.user.lastName = token.lastName as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n    error: '/auth/error',\n  }\n}\n\n// Load role permissions from database with caching\nasync function loadRolePermissions(): Promise<Record<string, string[]>> {\n  const now = Date.now()\n\n  // Return cached permissions if still valid\n  if (cacheLastUpdated && (now - cacheLastUpdated) < CACHE_DURATION) {\n    return rolePermissionsCache\n  }\n\n  try {\n    // Fetch all active role permissions from database\n    const permissions = await prisma.rolePermission.findMany({\n      where: { isActive: true },\n      select: { role: true, permission: true }\n    })\n\n    // Group permissions by role\n    const rolePermissions: Record<string, string[]> = {}\n    for (const perm of permissions) {\n      if (!rolePermissions[perm.role]) {\n        rolePermissions[perm.role] = []\n      }\n      rolePermissions[perm.role].push(perm.permission)\n    }\n\n    // Update cache\n    rolePermissionsCache = rolePermissions\n    cacheLastUpdated = now\n\n    return rolePermissions\n  } catch (error) {\n    console.error('Error loading role permissions from database:', error)\n    // Return empty permissions on error to be safe\n    return {}\n  }\n}\n\n// Get role permissions (cached)\nexport async function getRolePermissions(): Promise<Record<string, string[]>> {\n  return await loadRolePermissions()\n}\n\n// Clear permissions cache (call this when permissions are updated)\nexport function clearPermissionsCache() {\n  rolePermissionsCache = {}\n  cacheLastUpdated = 0\n}\n\n// Initialize permissions cache on server startup\nexport async function initializePermissionsCache() {\n  try {\n    await loadRolePermissions()\n    console.log('✅ Permissions cache initialized')\n  } catch (error) {\n    console.error('❌ Failed to initialize permissions cache:', error)\n  }\n}\n\nexport async function hasPermission(userRole: UserRole, permission: string): Promise<boolean> {\n  const rolePermissions = await getRolePermissions()\n  return rolePermissions[userRole]?.includes(permission) || false\n}\n\nexport async function checkPermissions(userRole: UserRole, requiredPermissions: string[]): Promise<boolean> {\n  const rolePermissions = await getRolePermissions()\n  return requiredPermissions.every(permission => rolePermissions[userRole]?.includes(permission) || false)\n}\n\n// Synchronous version for backward compatibility (uses cache)\nexport function hasPermissionSync(userRole: UserRole, permission: string): boolean {\n  return rolePermissionsCache[userRole]?.includes(permission) || false\n}\n\nexport function checkPermissionsSync(userRole: UserRole, requiredPermissions: string[]): boolean {\n  return requiredPermissions.every(permission => hasPermissionSync(userRole, permission))\n}\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AACA;AACA;AACA;;;;;AAGA,sEAAsE;AACtE,IAAI,uBAAiD,CAAC;AACtD,IAAI,mBAAmB;AACvB,MAAM,iBAAiB,IAAI,KAAK,KAAK,YAAY;;AAE1C,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;oBAC3B,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,oBAAoB;gBACpB,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;oBACvB,OAAO;wBAAE,IAAI,KAAK,EAAE;oBAAC;oBACrB,MAAM;wBAAE,WAAW,IAAI;oBAAO;gBAChC;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,WAAW,KAAK,SAAS;oBACzB,UAAU,KAAK,QAAQ;oBACvB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM,IAAI;gBACzB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;AACF;AAEA,mDAAmD;AACnD,eAAe;IACb,MAAM,MAAM,KAAK,GAAG;IAEpB,2CAA2C;IAC3C,IAAI,oBAAoB,AAAC,MAAM,mBAAoB,gBAAgB;QACjE,OAAO;IACT;IAEA,IAAI;QACF,kDAAkD;QAClD,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;YACvD,OAAO;gBAAE,UAAU;YAAK;YACxB,QAAQ;gBAAE,MAAM;gBAAM,YAAY;YAAK;QACzC;QAEA,4BAA4B;QAC5B,MAAM,kBAA4C,CAAC;QACnD,KAAK,MAAM,QAAQ,YAAa;YAC9B,IAAI,CAAC,eAAe,CAAC,KAAK,IAAI,CAAC,EAAE;gBAC/B,eAAe,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE;YACjC;YACA,eAAe,CAAC,KAAK,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,UAAU;QACjD;QAEA,eAAe;QACf,uBAAuB;QACvB,mBAAmB;QAEnB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iDAAiD;QAC/D,+CAA+C;QAC/C,OAAO,CAAC;IACV;AACF;AAGO,eAAe;IACpB,OAAO,MAAM;AACf;AAGO,SAAS;IACd,uBAAuB,CAAC;IACxB,mBAAmB;AACrB;AAGO,eAAe;IACpB,IAAI;QACF,MAAM;QACN,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;IAC7D;AACF;AAEO,eAAe,cAAc,QAAkB,EAAE,UAAkB;IACxE,MAAM,kBAAkB,MAAM;IAC9B,OAAO,eAAe,CAAC,SAAS,EAAE,SAAS,eAAe;AAC5D;AAEO,eAAe,iBAAiB,QAAkB,EAAE,mBAA6B;IACtF,MAAM,kBAAkB,MAAM;IAC9B,OAAO,oBAAoB,KAAK,CAAC,CAAA,aAAc,eAAe,CAAC,SAAS,EAAE,SAAS,eAAe;AACpG;AAGO,SAAS,kBAAkB,QAAkB,EAAE,UAAkB;IACtE,OAAO,oBAAoB,CAAC,SAAS,EAAE,SAAS,eAAe;AACjE;AAEO,SAAS,qBAAqB,QAAkB,EAAE,mBAA6B;IACpF,OAAO,oBAAoB,KAAK,CAAC,CAAA,aAAc,kBAAkB,UAAU;AAC7E", "debugId": null}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/lib/customer-status.ts"], "sourcesContent": ["import { prisma } from '@/lib/prisma'\nimport { CustomerStatus } from '@prisma/client'\n\n/**\n * Updates customer status based on their loan activity\n * Business Rules:\n * - New customers start as INACTIVE\n * - Customers with active loans (ACTIVE, DISBURSED) become ACTIVE\n * - Customers without active loans become INACTIVE (unless manually set to SUSPENDED/BLACKLISTED)\n * - SUSPENDED and BLACKLISTED statuses can only be changed manually by authorized roles\n */\nexport async function updateCustomerStatus(customerId: string, updatedBy?: string): Promise<CustomerStatus> {\n  try {\n    // Get customer with their loans\n    const customer = await prisma.customer.findUnique({\n      where: { id: customerId },\n      include: {\n        loans: {\n          where: {\n            status: {\n              in: ['ACTIVE', 'DISBURSED']\n            }\n          }\n        }\n      }\n    })\n\n    if (!customer) {\n      throw new Error('Customer not found')\n    }\n\n    // Don't auto-update if customer is manually suspended or blacklisted\n    if (customer.status === 'SUSPENDED' || customer.status === 'BLACKLISTED') {\n      return customer.status\n    }\n\n    // Determine new status based on active loans\n    const newStatus: CustomerStatus = customer.loans.length > 0 ? 'ACTIVE' : 'INACTIVE'\n\n    // Only update if status has changed\n    if (customer.status !== newStatus) {\n      await prisma.customer.update({\n        where: { id: customerId },\n        data: {\n          status: newStatus,\n          statusUpdatedBy: updatedBy || null,\n          statusUpdatedAt: new Date(),\n          statusNotes: `Auto-updated based on loan activity`\n        }\n      })\n\n      // Create audit log for automatic status updates\n      if (updatedBy) {\n        await prisma.auditLog.create({\n          data: {\n            action: 'AUTO_UPDATE_STATUS',\n            resource: 'Customer',\n            resourceId: customerId,\n            userId: updatedBy,\n            oldValues: {\n              status: customer.status\n            },\n            newValues: {\n              status: newStatus,\n              reason: 'Automatic update based on loan activity'\n            }\n          }\n        })\n      }\n    }\n\n    return newStatus\n  } catch (error) {\n    console.error('Error updating customer status:', error)\n    throw error\n  }\n}\n\n/**\n * Get customer status with additional context\n */\nexport async function getCustomerStatusInfo(customerId: string) {\n  const customer = await prisma.customer.findUnique({\n    where: { id: customerId },\n    include: {\n      loans: {\n        where: {\n          status: {\n            in: ['ACTIVE', 'DISBURSED']\n          }\n        },\n        select: {\n          id: true,\n          loanNumber: true,\n          status: true,\n          principalAmount: true,\n          disbursedAmount: true\n        }\n      }\n    }\n  })\n\n  if (!customer) {\n    return null\n  }\n\n  const statusInfo = {\n    currentStatus: customer.status,\n    activeLoansCount: customer.loans.length,\n    totalActiveAmount: customer.loans.reduce((sum, loan) => \n      sum + Number(loan.disbursedAmount || loan.principalAmount), 0\n    ),\n    canBeSetToActive: customer.loans.length > 0,\n    lastStatusUpdate: customer.statusUpdatedAt,\n    statusNotes: customer.statusNotes\n  }\n\n  return {\n    customer,\n    statusInfo\n  }\n}\n\n/**\n * Validate if a status change is allowed\n */\nexport function validateStatusChange(\n  currentStatus: CustomerStatus,\n  newStatus: CustomerStatus,\n  userRole: string,\n  hasActiveLoans: boolean\n): { isValid: boolean; reason?: string } {\n  // Only certain roles can set SUSPENDED or BLACKLISTED\n  if ((newStatus === 'SUSPENDED' || newStatus === 'BLACKLISTED') && \n      !['SUPER_ADMIN', 'HIGHER_MANAGEMENT', 'MANAGER'].includes(userRole)) {\n    return {\n      isValid: false,\n      reason: 'Insufficient permissions to suspend or blacklist customers'\n    }\n  }\n\n  // Cannot set to ACTIVE without active loans\n  if (newStatus === 'ACTIVE' && !hasActiveLoans) {\n    return {\n      isValid: false,\n      reason: 'Cannot set customer to ACTIVE status without active loans'\n    }\n  }\n\n  // All other changes are valid\n  return { isValid: true }\n}\n\n/**\n * Get status badge color for UI\n */\nexport function getStatusBadgeColor(status: CustomerStatus): string {\n  switch (status) {\n    case 'ACTIVE':\n      return 'bg-green-100 text-green-800'\n    case 'INACTIVE':\n      return 'bg-gray-100 text-gray-800'\n    case 'SUSPENDED':\n      return 'bg-yellow-100 text-yellow-800'\n    case 'BLACKLISTED':\n      return 'bg-red-100 text-red-800'\n    default:\n      return 'bg-gray-100 text-gray-800'\n  }\n}\n\n/**\n * Get status display label\n */\nexport function getStatusLabel(status: CustomerStatus): string {\n  switch (status) {\n    case 'ACTIVE':\n      return 'Active'\n    case 'INACTIVE':\n      return 'Inactive'\n    case 'SUSPENDED':\n      return 'Suspended'\n    case 'BLACKLISTED':\n      return 'Blacklisted'\n    default:\n      return 'Unknown'\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAWO,eAAe,qBAAqB,UAAkB,EAAE,SAAkB;IAC/E,IAAI;QACF,gCAAgC;QAChC,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YAChD,OAAO;gBAAE,IAAI;YAAW;YACxB,SAAS;gBACP,OAAO;oBACL,OAAO;wBACL,QAAQ;4BACN,IAAI;gCAAC;gCAAU;6BAAY;wBAC7B;oBACF;gBACF;YACF;QACF;QAEA,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,qEAAqE;QACrE,IAAI,SAAS,MAAM,KAAK,eAAe,SAAS,MAAM,KAAK,eAAe;YACxE,OAAO,SAAS,MAAM;QACxB;QAEA,6CAA6C;QAC7C,MAAM,YAA4B,SAAS,KAAK,CAAC,MAAM,GAAG,IAAI,WAAW;QAEzE,oCAAoC;QACpC,IAAI,SAAS,MAAM,KAAK,WAAW;YACjC,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAC3B,OAAO;oBAAE,IAAI;gBAAW;gBACxB,MAAM;oBACJ,QAAQ;oBACR,iBAAiB,aAAa;oBAC9B,iBAAiB,IAAI;oBACrB,aAAa,CAAC,mCAAmC,CAAC;gBACpD;YACF;YAEA,gDAAgD;YAChD,IAAI,WAAW;gBACb,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;oBAC3B,MAAM;wBACJ,QAAQ;wBACR,UAAU;wBACV,YAAY;wBACZ,QAAQ;wBACR,WAAW;4BACT,QAAQ,SAAS,MAAM;wBACzB;wBACA,WAAW;4BACT,QAAQ;4BACR,QAAQ;wBACV;oBACF;gBACF;YACF;QACF;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAKO,eAAe,sBAAsB,UAAkB;IAC5D,MAAM,WAAW,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;QAChD,OAAO;YAAE,IAAI;QAAW;QACxB,SAAS;YACP,OAAO;gBACL,OAAO;oBACL,QAAQ;wBACN,IAAI;4BAAC;4BAAU;yBAAY;oBAC7B;gBACF;gBACA,QAAQ;oBACN,IAAI;oBACJ,YAAY;oBACZ,QAAQ;oBACR,iBAAiB;oBACjB,iBAAiB;gBACnB;YACF;QACF;IACF;IAEA,IAAI,CAAC,UAAU;QACb,OAAO;IACT;IAEA,MAAM,aAAa;QACjB,eAAe,SAAS,MAAM;QAC9B,kBAAkB,SAAS,KAAK,CAAC,MAAM;QACvC,mBAAmB,SAAS,KAAK,CAAC,MAAM,CAAC,CAAC,KAAK,OAC7C,MAAM,OAAO,KAAK,eAAe,IAAI,KAAK,eAAe,GAAG;QAE9D,kBAAkB,SAAS,KAAK,CAAC,MAAM,GAAG;QAC1C,kBAAkB,SAAS,eAAe;QAC1C,aAAa,SAAS,WAAW;IACnC;IAEA,OAAO;QACL;QACA;IACF;AACF;AAKO,SAAS,qBACd,aAA6B,EAC7B,SAAyB,EACzB,QAAgB,EAChB,cAAuB;IAEvB,sDAAsD;IACtD,IAAI,CAAC,cAAc,eAAe,cAAc,aAAa,KACzD,CAAC;QAAC;QAAe;QAAqB;KAAU,CAAC,QAAQ,CAAC,WAAW;QACvE,OAAO;YACL,SAAS;YACT,QAAQ;QACV;IACF;IAEA,4CAA4C;IAC5C,IAAI,cAAc,YAAY,CAAC,gBAAgB;QAC7C,OAAO;YACL,SAAS;YACT,QAAQ;QACV;IACF;IAEA,8BAA8B;IAC9B,OAAO;QAAE,SAAS;IAAK;AACzB;AAKO,SAAS,oBAAoB,MAAsB;IACxD,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAKO,SAAS,eAAe,MAAsB;IACnD,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 509, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/lib/interest-calculations.ts"], "sourcesContent": ["/**\n * Interest calculation utilities for different loan calculation methods\n */\n\nexport interface LoanCalculationInput {\n  principalAmount: number\n  interestRate: number // Monthly percentage rate for MONTHLY_INTEREST method, Annual for COMPOUND_INTEREST\n  tenureInDays: number\n  collectionType: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'YEARLY'\n  interestCalculationMethod: 'MONTHLY_INTEREST' | 'COMPOUND_INTEREST'\n}\n\nexport interface LoanCalculationResult {\n  totalInterest: number\n  totalAmount: number\n  emiAmount: number\n  numberOfPayments: number\n}\n\n/**\n * Method 1: Monthly Interest Calculation\n * Formula: Principal × (Monthly Rate/100) × (Tenure in Months)\n * Example: 100,000 × 5% × 2 months = 10,000 interest\n * Note: interestRate is treated as MONTHLY rate for this method\n */\nfunction calculateMonthlyInterest(input: LoanCalculationInput): LoanCalculationResult {\n  const { principalAmount, interestRate, tenureInDays, collectionType } = input\n\n  // Convert tenure to months (30 days = 1 month)\n  const tenureInMonths = tenureInDays / 30\n\n  // Calculate total interest: Principal × Monthly Rate × Months\n  // interestRate is already monthly rate for this method\n  const totalInterest = principalAmount * (interestRate / 100) * tenureInMonths\n  \n  // Total amount to be repaid\n  const totalAmount = principalAmount + totalInterest\n  \n  // Calculate number of payments based on collection type\n  let numberOfPayments: number\n  switch (collectionType) {\n    case 'DAILY':\n      numberOfPayments = tenureInDays\n      break\n    case 'WEEKLY':\n      numberOfPayments = Math.ceil(tenureInDays / 7)\n      break\n    case 'MONTHLY':\n      numberOfPayments = Math.ceil(tenureInMonths)\n      break\n    case 'QUARTERLY':\n      numberOfPayments = Math.ceil(tenureInMonths / 3)\n      break\n    case 'YEARLY':\n      numberOfPayments = Math.ceil(tenureInMonths / 12)\n      break\n    default:\n      numberOfPayments = tenureInDays\n  }\n  \n  // EMI = Total Amount / Number of Payments\n  const emiAmount = totalAmount / numberOfPayments\n  \n  return {\n    totalInterest,\n    totalAmount,\n    emiAmount,\n    numberOfPayments\n  }\n}\n\n/**\n * Method 2: Compound Interest EMI Calculation\n * Formula: EMI = P × r × (1+r)^n / ((1+r)^n - 1)\n * This is the standard loan EMI calculation with compound interest\n * Note: interestRate is treated as ANNUAL rate for this method\n */\nfunction calculateCompoundInterest(input: LoanCalculationInput): LoanCalculationResult {\n  const { principalAmount, interestRate, tenureInDays, collectionType } = input\n\n  // Determine periods based on collection type\n  let periodsPerYear: number\n  let totalPeriods: number\n\n  switch (collectionType) {\n    case 'DAILY':\n      periodsPerYear = 365\n      totalPeriods = tenureInDays\n      break\n    case 'WEEKLY':\n      periodsPerYear = 52\n      totalPeriods = Math.ceil(tenureInDays / 7)\n      break\n    case 'MONTHLY':\n      periodsPerYear = 12\n      totalPeriods = Math.ceil(tenureInDays / 30)\n      break\n    case 'QUARTERLY':\n      periodsPerYear = 4\n      totalPeriods = Math.ceil(tenureInDays / 90)\n      break\n    case 'YEARLY':\n      periodsPerYear = 1\n      totalPeriods = Math.ceil(tenureInDays / 365)\n      break\n    default:\n      periodsPerYear = 12\n      totalPeriods = Math.ceil(tenureInDays / 30)\n  }\n\n  // Calculate period interest rate\n  const periodRate = interestRate / 100 / periodsPerYear\n\n  // Calculate EMI using compound interest formula\n  let emiAmount: number\n  if (periodRate === 0) {\n    // If no interest, just divide principal by periods\n    emiAmount = principalAmount / totalPeriods\n  } else {\n    // Standard EMI formula: EMI = P × r × (1+r)^n / ((1+r)^n - 1)\n    emiAmount = (principalAmount * periodRate * Math.pow(1 + periodRate, totalPeriods)) /\n                (Math.pow(1 + periodRate, totalPeriods) - 1)\n  }\n\n  // Calculate total amount and interest\n  const totalAmount = emiAmount * totalPeriods\n  const totalInterest = totalAmount - principalAmount\n\n  return {\n    totalInterest,\n    totalAmount,\n    emiAmount,\n    numberOfPayments: totalPeriods\n  }\n}\n\n/**\n * Main calculation function that routes to appropriate method\n */\nexport function calculateLoanInterest(input: LoanCalculationInput): LoanCalculationResult {\n  switch (input.interestCalculationMethod) {\n    case 'MONTHLY_INTEREST':\n      return calculateMonthlyInterest(input)\n    case 'COMPOUND_INTEREST':\n      return calculateCompoundInterest(input)\n    default:\n      throw new Error(`Unsupported interest calculation method: ${input.interestCalculationMethod}`)\n  }\n}\n\n/**\n * Helper function to convert tenure to days based on unit\n */\nexport function convertTenureToDays(tenure: number, unit: string): number {\n  switch (unit) {\n    case 'DAYS': return tenure\n    case 'WEEKS': return tenure * 7\n    case 'MONTHS': return tenure * 30\n    case 'YEARS': return tenure * 365\n    default: return tenure\n  }\n}\n\n/**\n * Helper function to get EMI frequency label\n */\nexport function getEMIFrequencyLabel(collectionType: string): string {\n  switch (collectionType) {\n    case 'DAILY': return 'Daily Payment'\n    case 'WEEKLY': return 'Weekly EMI'\n    case 'MONTHLY': return 'Monthly EMI'\n    case 'QUARTERLY': return 'Quarterly EMI'\n    case 'YEARLY': return 'Yearly EMI'\n    default: return 'Payment'\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAiBD;;;;;CAKC,GACD,SAAS,yBAAyB,KAA2B;IAC3D,MAAM,EAAE,eAAe,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,GAAG;IAExE,+CAA+C;IAC/C,MAAM,iBAAiB,eAAe;IAEtC,8DAA8D;IAC9D,uDAAuD;IACvD,MAAM,gBAAgB,kBAAkB,CAAC,eAAe,GAAG,IAAI;IAE/D,4BAA4B;IAC5B,MAAM,cAAc,kBAAkB;IAEtC,wDAAwD;IACxD,IAAI;IACJ,OAAQ;QACN,KAAK;YACH,mBAAmB;YACnB;QACF,KAAK;YACH,mBAAmB,KAAK,IAAI,CAAC,eAAe;YAC5C;QACF,KAAK;YACH,mBAAmB,KAAK,IAAI,CAAC;YAC7B;QACF,KAAK;YACH,mBAAmB,KAAK,IAAI,CAAC,iBAAiB;YAC9C;QACF,KAAK;YACH,mBAAmB,KAAK,IAAI,CAAC,iBAAiB;YAC9C;QACF;YACE,mBAAmB;IACvB;IAEA,0CAA0C;IAC1C,MAAM,YAAY,cAAc;IAEhC,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAEA;;;;;CAKC,GACD,SAAS,0BAA0B,KAA2B;IAC5D,MAAM,EAAE,eAAe,EAAE,YAAY,EAAE,YAAY,EAAE,cAAc,EAAE,GAAG;IAExE,6CAA6C;IAC7C,IAAI;IACJ,IAAI;IAEJ,OAAQ;QACN,KAAK;YACH,iBAAiB;YACjB,eAAe;YACf;QACF,KAAK;YACH,iBAAiB;YACjB,eAAe,KAAK,IAAI,CAAC,eAAe;YACxC;QACF,KAAK;YACH,iBAAiB;YACjB,eAAe,KAAK,IAAI,CAAC,eAAe;YACxC;QACF,KAAK;YACH,iBAAiB;YACjB,eAAe,KAAK,IAAI,CAAC,eAAe;YACxC;QACF,KAAK;YACH,iBAAiB;YACjB,eAAe,KAAK,IAAI,CAAC,eAAe;YACxC;QACF;YACE,iBAAiB;YACjB,eAAe,KAAK,IAAI,CAAC,eAAe;IAC5C;IAEA,iCAAiC;IACjC,MAAM,aAAa,eAAe,MAAM;IAExC,gDAAgD;IAChD,IAAI;IACJ,IAAI,eAAe,GAAG;QACpB,mDAAmD;QACnD,YAAY,kBAAkB;IAChC,OAAO;QACL,8DAA8D;QAC9D,YAAY,AAAC,kBAAkB,aAAa,KAAK,GAAG,CAAC,IAAI,YAAY,gBACzD,CAAC,KAAK,GAAG,CAAC,IAAI,YAAY,gBAAgB,CAAC;IACzD;IAEA,sCAAsC;IACtC,MAAM,cAAc,YAAY;IAChC,MAAM,gBAAgB,cAAc;IAEpC,OAAO;QACL;QACA;QACA;QACA,kBAAkB;IACpB;AACF;AAKO,SAAS,sBAAsB,KAA2B;IAC/D,OAAQ,MAAM,yBAAyB;QACrC,KAAK;YACH,OAAO,yBAAyB;QAClC,KAAK;YACH,OAAO,0BAA0B;QACnC;YACE,MAAM,IAAI,MAAM,CAAC,yCAAyC,EAAE,MAAM,yBAAyB,EAAE;IACjG;AACF;AAKO,SAAS,oBAAoB,MAAc,EAAE,IAAY;IAC9D,OAAQ;QACN,KAAK;YAAQ,OAAO;QACpB,KAAK;YAAS,OAAO,SAAS;QAC9B,KAAK;YAAU,OAAO,SAAS;QAC/B,KAAK;YAAS,OAAO,SAAS;QAC9B;YAAS,OAAO;IAClB;AACF;AAKO,SAAS,qBAAqB,cAAsB;IACzD,OAAQ;QACN,KAAK;YAAS,OAAO;QACrB,KAAK;YAAU,OAAO;QACtB,KAAK;YAAW,OAAO;QACvB,KAAK;YAAa,OAAO;QACzB,KAAK;YAAU,OAAO;QACtB;YAAS,OAAO;IAClB;AACF", "debugId": null}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/lib/payment-schedule.ts"], "sourcesContent": ["/**\n * Unified payment schedule generation that uses the correct interest calculation method\n */\n\nimport { calculateLoanInterest, convertTenureToDays } from './interest-calculations'\n\nexport interface LoanData {\n  id: string\n  principalAmount: number\n  interestRate: number\n  tenure: number\n  tenureUnit: string\n  repaymentFrequency: string\n  interestCalculationMethod: 'MONTHLY_INTEREST' | 'COMPOUND_INTEREST'\n  applicationDate?: Date\n  disbursementDate?: Date\n}\n\nexport interface PaymentScheduleItem {\n  installmentNumber: number\n  dueDate: Date\n  principalAmount: number\n  interestAmount: number\n  totalAmount: number\n  paidAmount: number\n  status: 'PENDING' | 'PAID' | 'PARTIAL' | 'OVERDUE'\n}\n\n/**\n * Generate payment schedule using the loan's specified interest calculation method\n */\nexport function generatePaymentSchedule(loan: LoanData): PaymentScheduleItem[] {\n  const principal = Number(loan.principalAmount)\n  const interestRate = Number(loan.interestRate)\n  const tenure = Number(loan.tenure)\n  \n  // Convert tenure to days\n  const tenureInDays = convertTenureToDays(tenure, loan.tenureUnit)\n  \n  // Map repayment frequency to collection type\n  const collectionType = mapRepaymentFrequencyToCollectionType(loan.repaymentFrequency)\n  \n  // Calculate loan details using the specified method\n  const calculation = calculateLoanInterest({\n    principalAmount: principal,\n    interestRate: interestRate,\n    tenureInDays: tenureInDays,\n    collectionType: collectionType,\n    interestCalculationMethod: loan.interestCalculationMethod\n  })\n  \n  // Generate schedule based on the calculation method\n  if (loan.interestCalculationMethod === 'MONTHLY_INTEREST') {\n    return generateMonthlyInterestSchedule(loan, calculation)\n  } else {\n    return generateCompoundInterestSchedule(loan, calculation)\n  }\n}\n\n/**\n * Generate schedule for Monthly Interest method (Simple Interest)\n * Each payment has equal installment amount with varying principal/interest split\n */\nfunction generateMonthlyInterestSchedule(loan: LoanData, calculation: any): PaymentScheduleItem[] {\n  const schedule: PaymentScheduleItem[] = []\n  const startDate = new Date(loan.disbursementDate || loan.applicationDate || new Date())\n  \n  // For monthly interest, each payment is the same amount\n  const installmentAmount = calculation.emiAmount\n  const totalInterest = calculation.totalInterest\n  const numberOfPayments = calculation.numberOfPayments\n  \n  // Distribute interest evenly across payments (simple interest approach)\n  const interestPerPayment = totalInterest / numberOfPayments\n  const principalPerPayment = (Number(loan.principalAmount) / numberOfPayments)\n  \n  for (let i = 1; i <= numberOfPayments; i++) {\n    const dueDate = getNextDueDate(startDate, loan.repaymentFrequency, i)\n    \n    schedule.push({\n      installmentNumber: i,\n      dueDate,\n      principalAmount: Math.round(principalPerPayment * 100) / 100,\n      interestAmount: Math.round(interestPerPayment * 100) / 100,\n      totalAmount: Math.round(installmentAmount * 100) / 100,\n      paidAmount: 0,\n      status: 'PENDING'\n    })\n  }\n  \n  return schedule\n}\n\n/**\n * Generate schedule for Compound Interest method (EMI)\n * Principal and interest amounts vary each payment, but total EMI remains constant\n */\nfunction generateCompoundInterestSchedule(loan: LoanData, calculation: any): PaymentScheduleItem[] {\n  const schedule: PaymentScheduleItem[] = []\n  const principal = Number(loan.principalAmount)\n  const annualRate = Number(loan.interestRate) / 100\n  const startDate = new Date(loan.disbursementDate || loan.applicationDate || new Date())\n  \n  // Determine period rate based on frequency\n  let periodsPerYear: number\n  switch (loan.repaymentFrequency) {\n    case 'DAILY': periodsPerYear = 365; break\n    case 'WEEKLY': periodsPerYear = 52; break\n    case 'MONTHLY': periodsPerYear = 12; break\n    case 'QUARTERLY': periodsPerYear = 4; break\n    case 'YEARLY': periodsPerYear = 1; break\n    default: periodsPerYear = 12\n  }\n  \n  const periodRate = annualRate / periodsPerYear\n  const emiAmount = calculation.emiAmount\n  const numberOfPayments = calculation.numberOfPayments\n  \n  let remainingPrincipal = principal\n  \n  for (let i = 1; i <= numberOfPayments; i++) {\n    const dueDate = getNextDueDate(startDate, loan.repaymentFrequency, i)\n    \n    // Calculate interest for this period\n    const interestAmount = remainingPrincipal * periodRate\n    \n    // Calculate principal payment (EMI - Interest)\n    const principalAmount = Math.min(emiAmount - interestAmount, remainingPrincipal)\n    \n    // Update remaining principal\n    remainingPrincipal -= principalAmount\n    \n    schedule.push({\n      installmentNumber: i,\n      dueDate,\n      principalAmount: Math.round(principalAmount * 100) / 100,\n      interestAmount: Math.round(interestAmount * 100) / 100,\n      totalAmount: Math.round(emiAmount * 100) / 100,\n      paidAmount: 0,\n      status: 'PENDING'\n    })\n    \n    // Break if principal is fully paid\n    if (remainingPrincipal <= 0.01) break\n  }\n  \n  return schedule\n}\n\n/**\n * Calculate next due date based on frequency\n */\nfunction getNextDueDate(startDate: Date, frequency: string, installmentNumber: number): Date {\n  const date = new Date(startDate)\n  \n  switch (frequency) {\n    case 'DAILY':\n      // For daily payments, add the installment number as calendar days\n      // This ensures that a 90-day loan has exactly 90 daily payments\n      date.setDate(date.getDate() + installmentNumber)\n      return date\n      \n    case 'WEEKLY':\n      const weeklyDate = new Date(date)\n      weeklyDate.setDate(weeklyDate.getDate() + (installmentNumber * 7))\n      return weeklyDate\n      \n    case 'MONTHLY':\n      const monthlyDate = new Date(date)\n      monthlyDate.setMonth(monthlyDate.getMonth() + installmentNumber)\n      return monthlyDate\n      \n    case 'QUARTERLY':\n      const quarterlyDate = new Date(date)\n      quarterlyDate.setMonth(quarterlyDate.getMonth() + (installmentNumber * 3))\n      return quarterlyDate\n      \n    case 'YEARLY':\n      const yearlyDate = new Date(date)\n      yearlyDate.setFullYear(yearlyDate.getFullYear() + installmentNumber)\n      return yearlyDate\n      \n    default:\n      const defaultDate = new Date(date)\n      defaultDate.setMonth(defaultDate.getMonth() + installmentNumber)\n      return defaultDate\n  }\n}\n\n/**\n * Map repayment frequency to collection type for interest calculations\n */\nfunction mapRepaymentFrequencyToCollectionType(frequency: string): 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'YEARLY' {\n  switch (frequency) {\n    case 'DAILY': return 'DAILY'\n    case 'WEEKLY': return 'WEEKLY'\n    case 'MONTHLY': return 'MONTHLY'\n    case 'QUARTERLY': return 'QUARTERLY'\n    case 'YEARLY': return 'YEARLY'\n    default: return 'MONTHLY'\n  }\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;AAED;;AA2BO,SAAS,wBAAwB,IAAc;IACpD,MAAM,YAAY,OAAO,KAAK,eAAe;IAC7C,MAAM,eAAe,OAAO,KAAK,YAAY;IAC7C,MAAM,SAAS,OAAO,KAAK,MAAM;IAEjC,yBAAyB;IACzB,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,KAAK,UAAU;IAEhE,6CAA6C;IAC7C,MAAM,iBAAiB,sCAAsC,KAAK,kBAAkB;IAEpF,oDAAoD;IACpD,MAAM,cAAc,CAAA,GAAA,wIAAA,CAAA,wBAAqB,AAAD,EAAE;QACxC,iBAAiB;QACjB,cAAc;QACd,cAAc;QACd,gBAAgB;QAChB,2BAA2B,KAAK,yBAAyB;IAC3D;IAEA,oDAAoD;IACpD,IAAI,KAAK,yBAAyB,KAAK,oBAAoB;QACzD,OAAO,gCAAgC,MAAM;IAC/C,OAAO;QACL,OAAO,iCAAiC,MAAM;IAChD;AACF;AAEA;;;CAGC,GACD,SAAS,gCAAgC,IAAc,EAAE,WAAgB;IACvE,MAAM,WAAkC,EAAE;IAC1C,MAAM,YAAY,IAAI,KAAK,KAAK,gBAAgB,IAAI,KAAK,eAAe,IAAI,IAAI;IAEhF,wDAAwD;IACxD,MAAM,oBAAoB,YAAY,SAAS;IAC/C,MAAM,gBAAgB,YAAY,aAAa;IAC/C,MAAM,mBAAmB,YAAY,gBAAgB;IAErD,wEAAwE;IACxE,MAAM,qBAAqB,gBAAgB;IAC3C,MAAM,sBAAuB,OAAO,KAAK,eAAe,IAAI;IAE5D,IAAK,IAAI,IAAI,GAAG,KAAK,kBAAkB,IAAK;QAC1C,MAAM,UAAU,eAAe,WAAW,KAAK,kBAAkB,EAAE;QAEnE,SAAS,IAAI,CAAC;YACZ,mBAAmB;YACnB;YACA,iBAAiB,KAAK,KAAK,CAAC,sBAAsB,OAAO;YACzD,gBAAgB,KAAK,KAAK,CAAC,qBAAqB,OAAO;YACvD,aAAa,KAAK,KAAK,CAAC,oBAAoB,OAAO;YACnD,YAAY;YACZ,QAAQ;QACV;IACF;IAEA,OAAO;AACT;AAEA;;;CAGC,GACD,SAAS,iCAAiC,IAAc,EAAE,WAAgB;IACxE,MAAM,WAAkC,EAAE;IAC1C,MAAM,YAAY,OAAO,KAAK,eAAe;IAC7C,MAAM,aAAa,OAAO,KAAK,YAAY,IAAI;IAC/C,MAAM,YAAY,IAAI,KAAK,KAAK,gBAAgB,IAAI,KAAK,eAAe,IAAI,IAAI;IAEhF,2CAA2C;IAC3C,IAAI;IACJ,OAAQ,KAAK,kBAAkB;QAC7B,KAAK;YAAS,iBAAiB;YAAK;QACpC,KAAK;YAAU,iBAAiB;YAAI;QACpC,KAAK;YAAW,iBAAiB;YAAI;QACrC,KAAK;YAAa,iBAAiB;YAAG;QACtC,KAAK;YAAU,iBAAiB;YAAG;QACnC;YAAS,iBAAiB;IAC5B;IAEA,MAAM,aAAa,aAAa;IAChC,MAAM,YAAY,YAAY,SAAS;IACvC,MAAM,mBAAmB,YAAY,gBAAgB;IAErD,IAAI,qBAAqB;IAEzB,IAAK,IAAI,IAAI,GAAG,KAAK,kBAAkB,IAAK;QAC1C,MAAM,UAAU,eAAe,WAAW,KAAK,kBAAkB,EAAE;QAEnE,qCAAqC;QACrC,MAAM,iBAAiB,qBAAqB;QAE5C,+CAA+C;QAC/C,MAAM,kBAAkB,KAAK,GAAG,CAAC,YAAY,gBAAgB;QAE7D,6BAA6B;QAC7B,sBAAsB;QAEtB,SAAS,IAAI,CAAC;YACZ,mBAAmB;YACnB;YACA,iBAAiB,KAAK,KAAK,CAAC,kBAAkB,OAAO;YACrD,gBAAgB,KAAK,KAAK,CAAC,iBAAiB,OAAO;YACnD,aAAa,KAAK,KAAK,CAAC,YAAY,OAAO;YAC3C,YAAY;YACZ,QAAQ;QACV;QAEA,mCAAmC;QACnC,IAAI,sBAAsB,MAAM;IAClC;IAEA,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,eAAe,SAAe,EAAE,SAAiB,EAAE,iBAAyB;IACnF,MAAM,OAAO,IAAI,KAAK;IAEtB,OAAQ;QACN,KAAK;YACH,kEAAkE;YAClE,gEAAgE;YAChE,KAAK,OAAO,CAAC,KAAK,OAAO,KAAK;YAC9B,OAAO;QAET,KAAK;YACH,MAAM,aAAa,IAAI,KAAK;YAC5B,WAAW,OAAO,CAAC,WAAW,OAAO,KAAM,oBAAoB;YAC/D,OAAO;QAET,KAAK;YACH,MAAM,cAAc,IAAI,KAAK;YAC7B,YAAY,QAAQ,CAAC,YAAY,QAAQ,KAAK;YAC9C,OAAO;QAET,KAAK;YACH,MAAM,gBAAgB,IAAI,KAAK;YAC/B,cAAc,QAAQ,CAAC,cAAc,QAAQ,KAAM,oBAAoB;YACvE,OAAO;QAET,KAAK;YACH,MAAM,aAAa,IAAI,KAAK;YAC5B,WAAW,WAAW,CAAC,WAAW,WAAW,KAAK;YAClD,OAAO;QAET;YACE,MAAM,cAAc,IAAI,KAAK;YAC7B,YAAY,QAAQ,CAAC,YAAY,QAAQ,KAAK;YAC9C,OAAO;IACX;AACF;AAEA;;CAEC,GACD,SAAS,sCAAsC,SAAiB;IAC9D,OAAQ;QACN,KAAK;YAAS,OAAO;QACrB,KAAK;YAAU,OAAO;QACtB,KAAK;YAAW,OAAO;QACvB,KAAK;YAAa,OAAO;QACzB,KAAK;YAAU,OAAO;QACtB;YAAS,OAAO;IAClB;AACF", "debugId": null}}, {"offset": {"line": 826, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/app/api/loans/%5Bid%5D/disburse/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions, hasPermission } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\nimport { updateCustomerStatus } from '@/lib/customer-status'\nimport { generatePaymentSchedule } from '@/lib/payment-schedule'\nimport { z } from 'zod'\n\nconst disbursementSchema = z.object({\n  disbursedAmount: z.number().positive('Disbursed amount must be positive'),\n  disbursementMethod: z.enum(['CASH', 'BANK_TRANSFER', 'CHEQUE', 'ONLINE', 'MOBILE_PAYMENT']),\n  disbursementReference: z.string().optional(),\n  disbursementNotes: z.string().optional(),\n  disbursementDate: z.string().transform((str) => new Date(str)).optional(),\n})\n\nexport async function POST(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session || !await hasPermission(session.user.role, 'loans:disburse')) {\n      return NextResponse.json({ error: 'Unauthorized - Loan disbursement permission required' }, { status: 401 })\n    }\n\n    const { id } = await params\n    const body = await request.json()\n    const validatedData = disbursementSchema.parse(body)\n\n    // Check if loan exists and is approved\n    const loan = await prisma.loan.findUnique({\n      where: { id },\n      include: {\n        customer: {\n          select: {\n            firstName: true,\n            lastName: true,\n            email: true,\n            phone: true\n          }\n        },\n        loanType: {\n          select: {\n            interestCalculationMethod: true,\n            tenureUnit: true,\n            name: true\n          }\n        }\n      }\n    })\n\n    if (!loan) {\n      return NextResponse.json({ error: 'Loan not found' }, { status: 404 })\n    }\n\n    if (loan.status !== 'APPROVED') {\n      return NextResponse.json({ \n        error: `Loan cannot be disbursed. Current status: ${loan.status}. Only approved loans can be disbursed.` \n      }, { status: 400 })\n    }\n\n    // Validate disbursed amount doesn't exceed principal amount\n    if (validatedData.disbursedAmount > Number(loan.principalAmount)) {\n      return NextResponse.json({ \n        error: `Disbursed amount (${validatedData.disbursedAmount}) cannot exceed principal amount (${loan.principalAmount})` \n      }, { status: 400 })\n    }\n\n    // Update loan with disbursement details\n    const updatedLoan = await prisma.$transaction(async (tx) => {\n      // Update loan status and disbursement details\n      const loan = await tx.loan.update({\n        where: { id },\n        data: {\n          status: 'ACTIVE',\n          disbursedAmount: validatedData.disbursedAmount,\n          disbursementMethod: validatedData.disbursementMethod,\n          disbursementReference: validatedData.disbursementReference || null,\n          disbursementNotes: validatedData.disbursementNotes || null,\n          disbursementDate: validatedData.disbursementDate || new Date(),\n          disbursedBy: session.user.id,\n          disbursedAt: new Date(),\n          updatedAt: new Date()\n        },\n        include: {\n          customer: {\n            select: {\n              firstName: true,\n              lastName: true,\n              phone: true,\n              email: true\n            }\n          },\n          loanType: {\n            select: {\n              name: true\n            }\n          }\n        }\n      })\n\n      // Delete any existing payment schedules first to avoid unique constraint violations\n      await tx.paymentSchedule.deleteMany({\n        where: { loanId: loan.id }\n      })\n\n      // Create payment schedule now that loan is disbursed\n      const loanData = {\n        id: loan.id,\n        principalAmount: Number(loan.principalAmount),\n        interestRate: Number(loan.interestRate),\n        tenure: Number(loan.tenure),\n        tenureUnit: loan.loanType.tenureUnit || 'MONTHS',\n        repaymentFrequency: loan.repaymentFrequency,\n        interestCalculationMethod: loan.loanType.interestCalculationMethod || 'MONTHLY_INTEREST',\n        applicationDate: loan.applicationDate,\n        disbursementDate: validatedData.disbursementDate || new Date()\n      }\n\n      console.log('Creating payment schedule for loan:', {\n        loanId: loan.loanNumber,\n        loanNumber: loan.loanNumber,\n        loanData\n      })\n\n      const schedule = generatePaymentSchedule(loanData)\n\n      console.log('Generated payment schedule:', {\n        numberOfPayments: schedule.length,\n        firstPayment: schedule[0],\n        lastPayment: schedule[schedule.length - 1]\n      })\n\n      // Save the generated schedule to database\n      console.log('Saving payment schedule data:', schedule.slice(0, 2)) // Log first 2 items for debugging\n\n      await tx.paymentSchedule.createMany({\n        data: schedule.map(item => ({\n          loanId: loan.id,\n          installmentNumber: item.installmentNumber,\n          dueDate: item.dueDate,\n          principalAmount: item.principalAmount,\n          interestAmount: item.interestAmount,\n          totalAmount: item.totalAmount,\n          status: 'PENDING' as const\n        }))\n      })\n\n      // Create audit log entry\n      await tx.auditLog.create({\n        data: {\n          action: 'DISBURSE',\n          resource: 'Loan',\n          resourceId: id,\n          userId: session.user.id,\n          newValues: {\n            status: 'ACTIVE',\n            disbursedAmount: validatedData.disbursedAmount,\n            disbursementMethod: validatedData.disbursementMethod,\n            disbursementReference: validatedData.disbursementReference,\n            disbursementDate: validatedData.disbursementDate || new Date()\n          }\n        }\n      })\n\n      return loan\n    })\n\n    // Update customer status to ACTIVE since they now have a disbursed loan\n    updateCustomerStatus(loan.customerId, session.user.id).catch(error => {\n      console.error('Error updating customer status after disbursement:', error)\n    })\n\n    // TODO: Send notification to customer (email/SMS)\n    // TODO: Generate disbursement receipt/document\n\n    return NextResponse.json({\n      message: `Loan disbursed successfully`,\n      loan: updatedLoan,\n      disbursement: {\n        amount: validatedData.disbursedAmount,\n        method: validatedData.disbursementMethod,\n        reference: validatedData.disbursementReference,\n        date: validatedData.disbursementDate || new Date(),\n        disbursedBy: {\n          id: session.user.id,\n          name: `${session.user.firstName} ${session.user.lastName}`,\n          role: session.user.role\n        }\n      }\n    })\n\n  } catch (error) {\n    console.error('Loan disbursement error:', error)\n    \n    if (error instanceof z.ZodError) {\n      return NextResponse.json(\n        { error: 'Validation error', details: error.errors },\n        { status: 400 }\n      )\n    }\n\n    return NextResponse.json(\n      { error: 'Failed to disburse loan' },\n      { status: 500 }\n    )\n  }\n}\n\n// GET /api/loans/[id]/disburse - Get disbursement details\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session || !await hasPermission(session.user.role, 'loans:read')) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const { id } = await params\n\n    const loan = await prisma.loan.findUnique({\n      where: { id },\n      select: {\n        id: true,\n        loanNumber: true,\n        status: true,\n        principalAmount: true,\n        disbursedAmount: true,\n        disbursementMethod: true,\n        disbursementReference: true,\n        disbursementNotes: true,\n        disbursementDate: true,\n        disbursedAt: true,\n        disburser: {\n          select: {\n            firstName: true,\n            lastName: true,\n            role: true\n          }\n        }\n      }\n    })\n\n    if (!loan) {\n      return NextResponse.json({ error: 'Loan not found' }, { status: 404 })\n    }\n\n    return NextResponse.json({ loan })\n\n  } catch (error) {\n    console.error('Error fetching disbursement details:', error)\n    return NextResponse.json(\n      { error: 'Failed to fetch disbursement details' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEA,MAAM,qBAAqB,+KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAClC,iBAAiB,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACrC,oBAAoB,+KAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAQ;QAAiB;QAAU;QAAU;KAAiB;IAC1F,uBAAuB,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1C,mBAAmB,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACtC,kBAAkB,+KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,MAAQ,IAAI,KAAK,MAAM,QAAQ;AACzE;AAEO,eAAe,KACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,WAAW,CAAC,MAAM,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,IAAI,CAAC,IAAI,EAAE,mBAAmB;YACzE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAuD,GAAG;gBAAE,QAAQ;YAAI;QAC5G;QAEA,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,gBAAgB,mBAAmB,KAAK,CAAC;QAE/C,uCAAuC;QACvC,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE;YAAG;YACZ,SAAS;gBACP,UAAU;oBACR,QAAQ;wBACN,WAAW;wBACX,UAAU;wBACV,OAAO;wBACP,OAAO;oBACT;gBACF;gBACA,UAAU;oBACR,QAAQ;wBACN,2BAA2B;wBAC3B,YAAY;wBACZ,MAAM;oBACR;gBACF;YACF;QACF;QAEA,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiB,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,IAAI,KAAK,MAAM,KAAK,YAAY;YAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO,CAAC,0CAA0C,EAAE,KAAK,MAAM,CAAC,uCAAuC,CAAC;YAC1G,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,4DAA4D;QAC5D,IAAI,cAAc,eAAe,GAAG,OAAO,KAAK,eAAe,GAAG;YAChE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,OAAO,CAAC,kBAAkB,EAAE,cAAc,eAAe,CAAC,kCAAkC,EAAE,KAAK,eAAe,CAAC,CAAC,CAAC;YACvH,GAAG;gBAAE,QAAQ;YAAI;QACnB;QAEA,wCAAwC;QACxC,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;YACnD,8CAA8C;YAC9C,MAAM,OAAO,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;gBAChC,OAAO;oBAAE;gBAAG;gBACZ,MAAM;oBACJ,QAAQ;oBACR,iBAAiB,cAAc,eAAe;oBAC9C,oBAAoB,cAAc,kBAAkB;oBACpD,uBAAuB,cAAc,qBAAqB,IAAI;oBAC9D,mBAAmB,cAAc,iBAAiB,IAAI;oBACtD,kBAAkB,cAAc,gBAAgB,IAAI,IAAI;oBACxD,aAAa,QAAQ,IAAI,CAAC,EAAE;oBAC5B,aAAa,IAAI;oBACjB,WAAW,IAAI;gBACjB;gBACA,SAAS;oBACP,UAAU;wBACR,QAAQ;4BACN,WAAW;4BACX,UAAU;4BACV,OAAO;4BACP,OAAO;wBACT;oBACF;oBACA,UAAU;wBACR,QAAQ;4BACN,MAAM;wBACR;oBACF;gBACF;YACF;YAEA,oFAAoF;YACpF,MAAM,GAAG,eAAe,CAAC,UAAU,CAAC;gBAClC,OAAO;oBAAE,QAAQ,KAAK,EAAE;gBAAC;YAC3B;YAEA,qDAAqD;YACrD,MAAM,WAAW;gBACf,IAAI,KAAK,EAAE;gBACX,iBAAiB,OAAO,KAAK,eAAe;gBAC5C,cAAc,OAAO,KAAK,YAAY;gBACtC,QAAQ,OAAO,KAAK,MAAM;gBAC1B,YAAY,KAAK,QAAQ,CAAC,UAAU,IAAI;gBACxC,oBAAoB,KAAK,kBAAkB;gBAC3C,2BAA2B,KAAK,QAAQ,CAAC,yBAAyB,IAAI;gBACtE,iBAAiB,KAAK,eAAe;gBACrC,kBAAkB,cAAc,gBAAgB,IAAI,IAAI;YAC1D;YAEA,QAAQ,GAAG,CAAC,uCAAuC;gBACjD,QAAQ,KAAK,UAAU;gBACvB,YAAY,KAAK,UAAU;gBAC3B;YACF;YAEA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,0BAAuB,AAAD,EAAE;YAEzC,QAAQ,GAAG,CAAC,+BAA+B;gBACzC,kBAAkB,SAAS,MAAM;gBACjC,cAAc,QAAQ,CAAC,EAAE;gBACzB,aAAa,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE;YAC5C;YAEA,0CAA0C;YAC1C,QAAQ,GAAG,CAAC,iCAAiC,SAAS,KAAK,CAAC,GAAG,KAAI,kCAAkC;YAErG,MAAM,GAAG,eAAe,CAAC,UAAU,CAAC;gBAClC,MAAM,SAAS,GAAG,CAAC,CAAA,OAAQ,CAAC;wBAC1B,QAAQ,KAAK,EAAE;wBACf,mBAAmB,KAAK,iBAAiB;wBACzC,SAAS,KAAK,OAAO;wBACrB,iBAAiB,KAAK,eAAe;wBACrC,gBAAgB,KAAK,cAAc;wBACnC,aAAa,KAAK,WAAW;wBAC7B,QAAQ;oBACV,CAAC;YACH;YAEA,yBAAyB;YACzB,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;gBACvB,MAAM;oBACJ,QAAQ;oBACR,UAAU;oBACV,YAAY;oBACZ,QAAQ,QAAQ,IAAI,CAAC,EAAE;oBACvB,WAAW;wBACT,QAAQ;wBACR,iBAAiB,cAAc,eAAe;wBAC9C,oBAAoB,cAAc,kBAAkB;wBACpD,uBAAuB,cAAc,qBAAqB;wBAC1D,kBAAkB,cAAc,gBAAgB,IAAI,IAAI;oBAC1D;gBACF;YACF;YAEA,OAAO;QACT;QAEA,wEAAwE;QACxE,CAAA,GAAA,kIAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK,UAAU,EAAE,QAAQ,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;YAC3D,QAAQ,KAAK,CAAC,sDAAsD;QACtE;QAEA,kDAAkD;QAClD,+CAA+C;QAE/C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS,CAAC,2BAA2B,CAAC;YACtC,MAAM;YACN,cAAc;gBACZ,QAAQ,cAAc,eAAe;gBACrC,QAAQ,cAAc,kBAAkB;gBACxC,WAAW,cAAc,qBAAqB;gBAC9C,MAAM,cAAc,gBAAgB,IAAI,IAAI;gBAC5C,aAAa;oBACX,IAAI,QAAQ,IAAI,CAAC,EAAE;oBACnB,MAAM,GAAG,QAAQ,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,IAAI,CAAC,QAAQ,EAAE;oBAC1D,MAAM,QAAQ,IAAI,CAAC,IAAI;gBACzB;YACF;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAE1C,IAAI,iBAAiB,+KAAA,CAAA,IAAC,CAAC,QAAQ,EAAE;YAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;gBAAoB,SAAS,MAAM,MAAM;YAAC,GACnD;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA0B,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,WAAW,CAAC,MAAM,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,IAAI,CAAC,IAAI,EAAE,eAAe;YACrE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QAErB,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE;YAAG;YACZ,QAAQ;gBACN,IAAI;gBACJ,YAAY;gBACZ,QAAQ;gBACR,iBAAiB;gBACjB,iBAAiB;gBACjB,oBAAoB;gBACpB,uBAAuB;gBACvB,mBAAmB;gBACnB,kBAAkB;gBAClB,aAAa;gBACb,WAAW;oBACT,QAAQ;wBACN,WAAW;wBACX,UAAU;wBACV,MAAM;oBACR;gBACF;YACF;QACF;QAEA,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiB,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAK;IAElC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuC,GAChD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}