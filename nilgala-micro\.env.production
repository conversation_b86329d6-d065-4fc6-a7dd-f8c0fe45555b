# Nilgala Micro - Loan Management System Environment Configuration

# Database Configuration - Prisma PostgreSQL
DATABASE_URL="postgres://ad11ee6fa100998cd16ee03029644e68ad2841b230ffeedd8105a5ddfadc2fca:<EMAIL>:5432/?sslmode=require"

# NextAuth Configuration
NEXTAUTH_SECRET="your-super-secret-key-change-this-in-production"
NEXTAUTH_URL="http://localhost:3000"

# JWT Configuration
JWT_SECRET="your-jwt-secret-key-change-this-in-production"

# Cloudflare R2 Configuration (placeholder - replace with actual credentials)
CLOUDFLARE_R2_ENDPOINT="https://b6e9d709f59807a7692e9ac22e76c0b8.r2.cloudflarestorage.com/nilgala-micro-documents"
CLOUDFLARE_R2_ACCESS_KEY_ID="7fcdf6b6b0349ec35c34113b57267c58"
CLOUDFLARE_R2_SECRET_ACCESS_KEY="b4fe906446074448c9903c8e4bb2fde706edad9cd9c4319fd517b4a889696fe1"
CLOUDFLARE_R2_BUCKET_NAME="nilgala-micro-documents"

# Email Configuration (placeholder)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# SMS Configuration (placeholder)
TWILIO_ACCOUNT_SID="your-twilio-account-sid"
TWILIO_AUTH_TOKEN="your-twilio-auth-token"
TWILIO_PHONE_NUMBER="your-twilio-phone-number"

# Application Configuration
NODE_ENV="development"
PORT="3000"

# Redis Configuration (optional for caching)
REDIS_URL="redis://localhost:6379"