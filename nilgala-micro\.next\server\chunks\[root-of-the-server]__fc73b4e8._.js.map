{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Cred<PERSON><PERSON><PERSON>rovider from 'next-auth/providers/credentials'\nimport { PrismaAdapter } from '@auth/prisma-adapter'\nimport { prisma } from './prisma'\nimport bcrypt from 'bcryptjs'\nimport { UserRole } from '@prisma/client'\n\n// Cache for role permissions to avoid database calls on every request\nlet rolePermissionsCache: Record<string, string[]> = {}\nlet cacheLastUpdated = 0\nconst CACHE_DURATION = 5 * 60 * 1000 // 5 minutes\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma) as any,\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user || !user.isActive) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        // Update last login\n        await prisma.user.update({\n          where: { id: user.id },\n          data: { lastLogin: new Date() }\n        })\n\n        return {\n          id: user.id,\n          email: user.email,\n          firstName: user.firstName,\n          lastName: user.lastName,\n          role: user.role,\n          avatar: user.avatar || undefined,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n        token.firstName = user.firstName\n        token.lastName = user.lastName\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as UserRole\n        session.user.firstName = token.firstName as string\n        session.user.lastName = token.lastName as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n    error: '/auth/error',\n  }\n}\n\n// Load role permissions from database with caching\nasync function loadRolePermissions(): Promise<Record<string, string[]>> {\n  const now = Date.now()\n\n  // Return cached permissions if still valid\n  if (cacheLastUpdated && (now - cacheLastUpdated) < CACHE_DURATION) {\n    return rolePermissionsCache\n  }\n\n  try {\n    // Fetch all active role permissions from database\n    const permissions = await prisma.rolePermission.findMany({\n      where: { isActive: true },\n      select: { role: true, permission: true }\n    })\n\n    // Group permissions by role\n    const rolePermissions: Record<string, string[]> = {}\n    for (const perm of permissions) {\n      if (!rolePermissions[perm.role]) {\n        rolePermissions[perm.role] = []\n      }\n      rolePermissions[perm.role].push(perm.permission)\n    }\n\n    // Update cache\n    rolePermissionsCache = rolePermissions\n    cacheLastUpdated = now\n\n    return rolePermissions\n  } catch (error) {\n    console.error('Error loading role permissions from database:', error)\n    // Return empty permissions on error to be safe\n    return {}\n  }\n}\n\n// Get role permissions (cached)\nexport async function getRolePermissions(): Promise<Record<string, string[]>> {\n  return await loadRolePermissions()\n}\n\n// Clear permissions cache (call this when permissions are updated)\nexport function clearPermissionsCache() {\n  rolePermissionsCache = {}\n  cacheLastUpdated = 0\n}\n\n// Initialize permissions cache on server startup\nexport async function initializePermissionsCache() {\n  try {\n    await loadRolePermissions()\n    console.log('✅ Permissions cache initialized')\n  } catch (error) {\n    console.error('❌ Failed to initialize permissions cache:', error)\n  }\n}\n\nexport async function hasPermission(userRole: UserRole, permission: string): Promise<boolean> {\n  const rolePermissions = await getRolePermissions()\n  return rolePermissions[userRole]?.includes(permission) || false\n}\n\nexport async function checkPermissions(userRole: UserRole, requiredPermissions: string[]): Promise<boolean> {\n  const rolePermissions = await getRolePermissions()\n  return requiredPermissions.every(permission => rolePermissions[userRole]?.includes(permission) || false)\n}\n\n// Synchronous version for backward compatibility (uses cache)\nexport function hasPermissionSync(userRole: UserRole, permission: string): boolean {\n  return rolePermissionsCache[userRole]?.includes(permission) || false\n}\n\nexport function checkPermissionsSync(userRole: UserRole, requiredPermissions: string[]): boolean {\n  return requiredPermissions.every(permission => hasPermissionSync(userRole, permission))\n}\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AACA;AACA;AACA;;;;;AAGA,sEAAsE;AACtE,IAAI,uBAAiD,CAAC;AACtD,IAAI,mBAAmB;AACvB,MAAM,iBAAiB,IAAI,KAAK,KAAK,YAAY;;AAE1C,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;oBAC3B,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,oBAAoB;gBACpB,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;oBACvB,OAAO;wBAAE,IAAI,KAAK,EAAE;oBAAC;oBACrB,MAAM;wBAAE,WAAW,IAAI;oBAAO;gBAChC;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,WAAW,KAAK,SAAS;oBACzB,UAAU,KAAK,QAAQ;oBACvB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM,IAAI;gBACzB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;AACF;AAEA,mDAAmD;AACnD,eAAe;IACb,MAAM,MAAM,KAAK,GAAG;IAEpB,2CAA2C;IAC3C,IAAI,oBAAoB,AAAC,MAAM,mBAAoB,gBAAgB;QACjE,OAAO;IACT;IAEA,IAAI;QACF,kDAAkD;QAClD,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;YACvD,OAAO;gBAAE,UAAU;YAAK;YACxB,QAAQ;gBAAE,MAAM;gBAAM,YAAY;YAAK;QACzC;QAEA,4BAA4B;QAC5B,MAAM,kBAA4C,CAAC;QACnD,KAAK,MAAM,QAAQ,YAAa;YAC9B,IAAI,CAAC,eAAe,CAAC,KAAK,IAAI,CAAC,EAAE;gBAC/B,eAAe,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE;YACjC;YACA,eAAe,CAAC,KAAK,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,UAAU;QACjD;QAEA,eAAe;QACf,uBAAuB;QACvB,mBAAmB;QAEnB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iDAAiD;QAC/D,+CAA+C;QAC/C,OAAO,CAAC;IACV;AACF;AAGO,eAAe;IACpB,OAAO,MAAM;AACf;AAGO,SAAS;IACd,uBAAuB,CAAC;IACxB,mBAAmB;AACrB;AAGO,eAAe;IACpB,IAAI;QACF,MAAM;QACN,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;IAC7D;AACF;AAEO,eAAe,cAAc,QAAkB,EAAE,UAAkB;IACxE,MAAM,kBAAkB,MAAM;IAC9B,OAAO,eAAe,CAAC,SAAS,EAAE,SAAS,eAAe;AAC5D;AAEO,eAAe,iBAAiB,QAAkB,EAAE,mBAA6B;IACtF,MAAM,kBAAkB,MAAM;IAC9B,OAAO,oBAAoB,KAAK,CAAC,CAAA,aAAc,eAAe,CAAC,SAAS,EAAE,SAAS,eAAe;AACpG;AAGO,SAAS,kBAAkB,QAAkB,EAAE,UAAkB;IACtE,OAAO,oBAAoB,CAAC,SAAS,EAAE,SAAS,eAAe;AACjE;AAEO,SAAS,qBAAqB,QAAkB,EAAE,mBAA6B;IACpF,OAAO,oBAAoB,KAAK,CAAC,CAAA,aAAc,kBAAkB,UAAU;AAC7E", "debugId": null}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/app/api/performance/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions, hasPermission } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session || !await hasPermission(session.user.role, 'reports:read')) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const { searchParams } = new URL(request.url)\n    const year = parseInt(searchParams.get('year') || new Date().getFullYear().toString())\n    const period = searchParams.get('period') || 'monthly'\n\n    // Only Credit Officers can view their own performance\n    if (session.user.role !== 'CREDIT_OFFICER') {\n      return NextResponse.json({ error: 'Access denied' }, { status: 403 })\n    }\n\n    const performanceData = await getCreditOfficerPerformance(session.user.id, year, period)\n    \n    return NextResponse.json(performanceData)\n\n  } catch (error) {\n    console.error('Error fetching performance data:', error)\n    return NextResponse.json(\n      { error: 'Failed to fetch performance data' },\n      { status: 500 }\n    )\n  }\n}\n\nasync function getCreditOfficerPerformance(officerId: string, year: number, period: string) {\n  const currentDate = new Date()\n  const currentMonth = currentDate.getMonth() + 1\n  const currentYear = currentDate.getFullYear()\n\n  // Get current month target and achievement\n  const currentMonthTarget = await prisma.creditOfficerTarget.findUnique({\n    where: {\n      creditOfficerId_month_year: {\n        creditOfficerId: officerId,\n        month: currentMonth,\n        year: currentYear\n      }\n    }\n  })\n\n  // Calculate current month achievement (disbursed loans this month)\n  const startOfMonth = new Date(currentYear, currentMonth - 1, 1)\n  const endOfMonth = new Date(currentYear, currentMonth, 0, 23, 59, 59)\n\n  // Get achievement from loans where customer is assigned to officer\n  const currentMonthAchievement = await prisma.loan.aggregate({\n    _sum: { principalAmount: true },\n    where: {\n      customer: {\n        assignedTo: officerId\n      },\n      status: {\n        in: ['DISBURSED', 'ACTIVE', 'COMPLETED']\n      },\n      OR: [\n        {\n          disbursementDate: {\n            gte: startOfMonth,\n            lte: endOfMonth\n          }\n        },\n        {\n          approvalDate: {\n            gte: startOfMonth,\n            lte: endOfMonth\n          }\n        }\n      ]\n    }\n  })\n\n  const target = Number(currentMonthTarget?.loanTarget || 500000)\n  const achievement = Number(currentMonthAchievement._sum.principalAmount || 0)\n  const percentage = target > 0 ? (achievement / target) * 100 : 0\n\n  // Get team ranking\n  const allOfficers = await prisma.user.findMany({\n    where: {\n      role: 'CREDIT_OFFICER',\n      isActive: true\n    },\n    select: { id: true }\n  })\n\n  // Calculate achievements for all officers this month\n  const officerAchievements = await Promise.all(\n    allOfficers.map(async (officer) => {\n      const officerAchievement = await prisma.loan.aggregate({\n        _sum: { principalAmount: true },\n        where: {\n          customer: {\n            assignedTo: officer.id\n          },\n          status: {\n            in: ['DISBURSED', 'ACTIVE', 'COMPLETED']\n          },\n          OR: [\n            {\n              disbursementDate: {\n                gte: startOfMonth,\n                lte: endOfMonth\n              }\n            },\n            {\n              approvalDate: {\n                gte: startOfMonth,\n                lte: endOfMonth\n              }\n            }\n          ]\n        }\n      })\n      return {\n        officerId: officer.id,\n        achievement: Number(officerAchievement._sum.principalAmount || 0)\n      }\n    })\n  )\n\n  // Sort by achievement and find rank\n  officerAchievements.sort((a, b) => b.achievement - a.achievement)\n  const rank = officerAchievements.findIndex(o => o.officerId === officerId) + 1\n\n  // Get monthly history for the year\n  const monthlyHistory = []\n  for (let month = 1; month <= 12; month++) {\n    const monthTarget = await prisma.creditOfficerTarget.findUnique({\n      where: {\n        creditOfficerId_month_year: {\n          creditOfficerId: officerId,\n          month: month,\n          year: year\n        }\n      }\n    })\n\n    const monthStart = new Date(year, month - 1, 1)\n    const monthEnd = new Date(year, month, 0, 23, 59, 59)\n\n    const monthAchievement = await prisma.loan.aggregate({\n      _sum: { principalAmount: true },\n      where: {\n        customer: {\n          assignedTo: officerId\n        },\n        status: {\n          in: ['DISBURSED', 'ACTIVE', 'COMPLETED']\n        },\n        OR: [\n          {\n            disbursementDate: {\n              gte: monthStart,\n              lte: monthEnd\n            }\n          },\n          {\n            approvalDate: {\n              gte: monthStart,\n              lte: monthEnd\n            }\n          }\n        ]\n      }\n    })\n\n    const monthTargetAmount = Number(monthTarget?.loanTarget || 0)\n    const monthAchievementAmount = Number(monthAchievement._sum.principalAmount || 0)\n    const monthPercentage = monthTargetAmount > 0 ? (monthAchievementAmount / monthTargetAmount) * 100 : 0\n\n    if (monthTargetAmount > 0 || monthAchievementAmount > 0) {\n      monthlyHistory.push({\n        month: new Date(year, month - 1).toLocaleDateString('en-US', { month: 'long' }),\n        target: monthTargetAmount,\n        achievement: monthAchievementAmount,\n        percentage: monthPercentage\n      })\n    }\n  }\n\n  // Calculate yearly stats\n  const yearlyTargets = await prisma.creditOfficerTarget.findMany({\n    where: {\n      creditOfficerId: officerId,\n      year: year\n    }\n  })\n\n  const totalTarget = yearlyTargets.reduce((sum, target) => sum + Number(target.loanTarget), 0)\n  const totalAchievement = monthlyHistory.reduce((sum, month) => sum + month.achievement, 0)\n  const averagePercentage = monthlyHistory.length > 0 \n    ? monthlyHistory.reduce((sum, month) => sum + month.percentage, 0) / monthlyHistory.length \n    : 0\n\n  const bestMonth = monthlyHistory.reduce((best, month) => \n    month.percentage > best.percentage ? month : best, \n    { month: 'N/A', percentage: 0 }\n  )\n\n  // Get loan metrics\n  const loanMetrics = await prisma.loan.aggregate({\n    _count: true,\n    _sum: { principalAmount: true },\n    _avg: { principalAmount: true },\n    where: {\n      customer: {\n        assignedTo: officerId\n      }\n    }\n  })\n\n  const activeLoanCount = await prisma.loan.count({\n    where: {\n      customer: {\n        assignedTo: officerId\n      },\n      status: {\n        in: ['ACTIVE', 'DISBURSED']\n      }\n    }\n  })\n\n  const completedLoanCount = await prisma.loan.count({\n    where: {\n      customer: {\n        assignedTo: officerId\n      },\n      status: 'COMPLETED'\n    }\n  })\n\n  // Get customer metrics - customers assigned to this officer\n  const customerMetrics = await prisma.customer.aggregate({\n    _count: true,\n    where: {\n      assignedTo: officerId\n    }\n  })\n\n  const newCustomersThisMonth = await prisma.customer.count({\n    where: {\n      assignedTo: officerId,\n      createdAt: {\n        gte: startOfMonth,\n        lte: endOfMonth\n      }\n    }\n  })\n\n  // Calculate customers with loans (assigned to this officer)\n  const customersWithLoansFromOfficer = await prisma.customer.count({\n    where: {\n      assignedTo: officerId,\n      loans: {\n        some: {\n          status: {\n            in: ['COMPLETED', 'ACTIVE', 'DISBURSED', 'APPROVED']\n          }\n        }\n      }\n    }\n  })\n\n  // Calculate retention rate (customers with multiple loans)\n  const customersWithMultipleLoans = await prisma.customer.count({\n    where: {\n      assignedTo: officerId,\n      loans: {\n        some: {\n          status: {\n            in: ['COMPLETED', 'ACTIVE', 'DISBURSED']\n          }\n        }\n      }\n    }\n  })\n\n  const retentionRate = customerMetrics._count > 0\n    ? (customersWithMultipleLoans / customerMetrics._count) * 100\n    : 0\n\n  return {\n    currentMonth: {\n      target,\n      achievement,\n      percentage,\n      rank,\n      totalOfficers: allOfficers.length\n    },\n    monthlyHistory,\n    yearlyStats: {\n      totalTarget,\n      totalAchievement,\n      averagePercentage,\n      bestMonth: bestMonth.month,\n      bestPercentage: bestMonth.percentage\n    },\n    loanMetrics: {\n      totalLoans: loanMetrics._count || 0,\n      activeLoans: activeLoanCount,\n      completedLoans: completedLoanCount,\n      averageLoanAmount: Number(loanMetrics._avg.principalAmount || 0),\n      totalDisbursed: Number(loanMetrics._sum.principalAmount || 0)\n    },\n    customerMetrics: {\n      assignedCustomers: customerMetrics._count || 0,\n      customersWithLoans: customersWithLoansFromOfficer,\n      newCustomers: newCustomersThisMonth,\n      retentionRate,\n      satisfactionScore: 85 // Placeholder - could be calculated from feedback\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,WAAW,CAAC,MAAM,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,IAAI,CAAC,IAAI,EAAE,iBAAiB;YACvE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW,IAAI,OAAO,WAAW,GAAG,QAAQ;QACnF,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAE7C,sDAAsD;QACtD,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,kBAAkB;YAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAgB,GAAG;gBAAE,QAAQ;YAAI;QACrE;QAEA,MAAM,kBAAkB,MAAM,4BAA4B,QAAQ,IAAI,CAAC,EAAE,EAAE,MAAM;QAEjF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAmC,GAC5C;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,eAAe,4BAA4B,SAAiB,EAAE,IAAY,EAAE,MAAc;IACxF,MAAM,cAAc,IAAI;IACxB,MAAM,eAAe,YAAY,QAAQ,KAAK;IAC9C,MAAM,cAAc,YAAY,WAAW;IAE3C,2CAA2C;IAC3C,MAAM,qBAAqB,MAAM,sHAAA,CAAA,SAAM,CAAC,mBAAmB,CAAC,UAAU,CAAC;QACrE,OAAO;YACL,4BAA4B;gBAC1B,iBAAiB;gBACjB,OAAO;gBACP,MAAM;YACR;QACF;IACF;IAEA,mEAAmE;IACnE,MAAM,eAAe,IAAI,KAAK,aAAa,eAAe,GAAG;IAC7D,MAAM,aAAa,IAAI,KAAK,aAAa,cAAc,GAAG,IAAI,IAAI;IAElE,mEAAmE;IACnE,MAAM,0BAA0B,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;QAC1D,MAAM;YAAE,iBAAiB;QAAK;QAC9B,OAAO;YACL,UAAU;gBACR,YAAY;YACd;YACA,QAAQ;gBACN,IAAI;oBAAC;oBAAa;oBAAU;iBAAY;YAC1C;YACA,IAAI;gBACF;oBACE,kBAAkB;wBAChB,KAAK;wBACL,KAAK;oBACP;gBACF;gBACA;oBACE,cAAc;wBACZ,KAAK;wBACL,KAAK;oBACP;gBACF;aACD;QACH;IACF;IAEA,MAAM,SAAS,OAAO,oBAAoB,cAAc;IACxD,MAAM,cAAc,OAAO,wBAAwB,IAAI,CAAC,eAAe,IAAI;IAC3E,MAAM,aAAa,SAAS,IAAI,AAAC,cAAc,SAAU,MAAM;IAE/D,mBAAmB;IACnB,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC7C,OAAO;YACL,MAAM;YACN,UAAU;QACZ;QACA,QAAQ;YAAE,IAAI;QAAK;IACrB;IAEA,qDAAqD;IACrD,MAAM,sBAAsB,MAAM,QAAQ,GAAG,CAC3C,YAAY,GAAG,CAAC,OAAO;QACrB,MAAM,qBAAqB,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YACrD,MAAM;gBAAE,iBAAiB;YAAK;YAC9B,OAAO;gBACL,UAAU;oBACR,YAAY,QAAQ,EAAE;gBACxB;gBACA,QAAQ;oBACN,IAAI;wBAAC;wBAAa;wBAAU;qBAAY;gBAC1C;gBACA,IAAI;oBACF;wBACE,kBAAkB;4BAChB,KAAK;4BACL,KAAK;wBACP;oBACF;oBACA;wBACE,cAAc;4BACZ,KAAK;4BACL,KAAK;wBACP;oBACF;iBACD;YACH;QACF;QACA,OAAO;YACL,WAAW,QAAQ,EAAE;YACrB,aAAa,OAAO,mBAAmB,IAAI,CAAC,eAAe,IAAI;QACjE;IACF;IAGF,oCAAoC;IACpC,oBAAoB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,WAAW,GAAG,EAAE,WAAW;IAChE,MAAM,OAAO,oBAAoB,SAAS,CAAC,CAAA,IAAK,EAAE,SAAS,KAAK,aAAa;IAE7E,mCAAmC;IACnC,MAAM,iBAAiB,EAAE;IACzB,IAAK,IAAI,QAAQ,GAAG,SAAS,IAAI,QAAS;QACxC,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,mBAAmB,CAAC,UAAU,CAAC;YAC9D,OAAO;gBACL,4BAA4B;oBAC1B,iBAAiB;oBACjB,OAAO;oBACP,MAAM;gBACR;YACF;QACF;QAEA,MAAM,aAAa,IAAI,KAAK,MAAM,QAAQ,GAAG;QAC7C,MAAM,WAAW,IAAI,KAAK,MAAM,OAAO,GAAG,IAAI,IAAI;QAElD,MAAM,mBAAmB,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;YACnD,MAAM;gBAAE,iBAAiB;YAAK;YAC9B,OAAO;gBACL,UAAU;oBACR,YAAY;gBACd;gBACA,QAAQ;oBACN,IAAI;wBAAC;wBAAa;wBAAU;qBAAY;gBAC1C;gBACA,IAAI;oBACF;wBACE,kBAAkB;4BAChB,KAAK;4BACL,KAAK;wBACP;oBACF;oBACA;wBACE,cAAc;4BACZ,KAAK;4BACL,KAAK;wBACP;oBACF;iBACD;YACH;QACF;QAEA,MAAM,oBAAoB,OAAO,aAAa,cAAc;QAC5D,MAAM,yBAAyB,OAAO,iBAAiB,IAAI,CAAC,eAAe,IAAI;QAC/E,MAAM,kBAAkB,oBAAoB,IAAI,AAAC,yBAAyB,oBAAqB,MAAM;QAErG,IAAI,oBAAoB,KAAK,yBAAyB,GAAG;YACvD,eAAe,IAAI,CAAC;gBAClB,OAAO,IAAI,KAAK,MAAM,QAAQ,GAAG,kBAAkB,CAAC,SAAS;oBAAE,OAAO;gBAAO;gBAC7E,QAAQ;gBACR,aAAa;gBACb,YAAY;YACd;QACF;IACF;IAEA,yBAAyB;IACzB,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC;QAC9D,OAAO;YACL,iBAAiB;YACjB,MAAM;QACR;IACF;IAEA,MAAM,cAAc,cAAc,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,OAAO,UAAU,GAAG;IAC3F,MAAM,mBAAmB,eAAe,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,WAAW,EAAE;IACxF,MAAM,oBAAoB,eAAe,MAAM,GAAG,IAC9C,eAAe,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,UAAU,EAAE,KAAK,eAAe,MAAM,GACxF;IAEJ,MAAM,YAAY,eAAe,MAAM,CAAC,CAAC,MAAM,QAC7C,MAAM,UAAU,GAAG,KAAK,UAAU,GAAG,QAAQ,MAC7C;QAAE,OAAO;QAAO,YAAY;IAAE;IAGhC,mBAAmB;IACnB,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;QAC9C,QAAQ;QACR,MAAM;YAAE,iBAAiB;QAAK;QAC9B,MAAM;YAAE,iBAAiB;QAAK;QAC9B,OAAO;YACL,UAAU;gBACR,YAAY;YACd;QACF;IACF;IAEA,MAAM,kBAAkB,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;QAC9C,OAAO;YACL,UAAU;gBACR,YAAY;YACd;YACA,QAAQ;gBACN,IAAI;oBAAC;oBAAU;iBAAY;YAC7B;QACF;IACF;IAEA,MAAM,qBAAqB,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;QACjD,OAAO;YACL,UAAU;gBACR,YAAY;YACd;YACA,QAAQ;QACV;IACF;IAEA,4DAA4D;IAC5D,MAAM,kBAAkB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;QACtD,QAAQ;QACR,OAAO;YACL,YAAY;QACd;IACF;IAEA,MAAM,wBAAwB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;QACxD,OAAO;YACL,YAAY;YACZ,WAAW;gBACT,KAAK;gBACL,KAAK;YACP;QACF;IACF;IAEA,4DAA4D;IAC5D,MAAM,gCAAgC,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;QAChE,OAAO;YACL,YAAY;YACZ,OAAO;gBACL,MAAM;oBACJ,QAAQ;wBACN,IAAI;4BAAC;4BAAa;4BAAU;4BAAa;yBAAW;oBACtD;gBACF;YACF;QACF;IACF;IAEA,2DAA2D;IAC3D,MAAM,6BAA6B,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;QAC7D,OAAO;YACL,YAAY;YACZ,OAAO;gBACL,MAAM;oBACJ,QAAQ;wBACN,IAAI;4BAAC;4BAAa;4BAAU;yBAAY;oBAC1C;gBACF;YACF;QACF;IACF;IAEA,MAAM,gBAAgB,gBAAgB,MAAM,GAAG,IAC3C,AAAC,6BAA6B,gBAAgB,MAAM,GAAI,MACxD;IAEJ,OAAO;QACL,cAAc;YACZ;YACA;YACA;YACA;YACA,eAAe,YAAY,MAAM;QACnC;QACA;QACA,aAAa;YACX;YACA;YACA;YACA,WAAW,UAAU,KAAK;YAC1B,gBAAgB,UAAU,UAAU;QACtC;QACA,aAAa;YACX,YAAY,YAAY,MAAM,IAAI;YAClC,aAAa;YACb,gBAAgB;YAChB,mBAAmB,OAAO,YAAY,IAAI,CAAC,eAAe,IAAI;YAC9D,gBAAgB,OAAO,YAAY,IAAI,CAAC,eAAe,IAAI;QAC7D;QACA,iBAAiB;YACf,mBAAmB,gBAAgB,MAAM,IAAI;YAC7C,oBAAoB;YACpB,cAAc;YACd;YACA,mBAAmB,GAAG,kDAAkD;QAC1E;IACF;AACF", "debugId": null}}]}