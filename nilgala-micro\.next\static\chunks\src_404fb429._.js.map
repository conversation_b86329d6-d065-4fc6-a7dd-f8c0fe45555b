{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,KAAyD;QAAzD,EAAE,SAAS,EAAE,GAAG,OAAyC,GAAzD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 196, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 249, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACb,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,KAGY;QAHZ,EACpB,SAAS,EACT,GAAG,OAC6B,GAHZ;IAIpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 395, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\n\nimport { cn } from \"@/lib/utils\"\nimport { buttonVariants } from \"@/components/ui/button\"\n\nconst AlertDialog = AlertDialogPrimitive.Root\n\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\n\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\n\nconst AlertDialogOverlay = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\n\nconst AlertDialogContent = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPortal>\n    <AlertDialogOverlay />\n    <AlertDialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    />\n  </AlertDialogPortal>\n))\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\n\nconst AlertDialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\n\nconst AlertDialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\n\nconst AlertDialogTitle = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold\", className)}\n    {...props}\n  />\n))\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\n\nconst AlertDialogDescription = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nAlertDialogDescription.displayName =\n  AlertDialogPrimitive.Description.displayName\n\nconst AlertDialogAction = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Action\n    ref={ref}\n    className={cn(buttonVariants(), className)}\n    {...props}\n  />\n))\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\n\nconst AlertDialogCancel = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Cancel\n    ref={ref}\n    className={cn(\n      buttonVariants({ variant: \"outline\" }),\n      \"mt-2 sm:mt-0\",\n      className\n    )}\n    {...props}\n  />\n))\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\n\nexport {\n  AlertDialog,\n  AlertDialogPortal,\n  AlertDialogOverlay,\n  AlertDialogTrigger,\n  AlertDialogContent,\n  AlertDialogHeader,\n  AlertDialogFooter,\n  AlertDialogTitle,\n  AlertDialogDescription,\n  AlertDialogAction,\n  AlertDialogCancel,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQA,MAAM,cAAc,8KAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,8KAAA,CAAA,UAA4B;AAEvD,MAAM,oBAAoB,8KAAA,CAAA,SAA2B;AAErD,MAAM,mCAAqB,6JAAA,CAAA,aAAgB,CAGzC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,8KAAA,CAAA,UAA4B;QAC3B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;;KAVH;AAaN,mBAAmB,WAAW,GAAG,8KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,mCAAqB,6JAAA,CAAA,aAAgB,OAGzC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,8KAAA,CAAA,UAA4B;gBAC3B,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;;;;;;;;;;;;;AAIf,mBAAmB,WAAW,GAAG,8KAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,oBAAoB;QAAC,EACzB,SAAS,EACT,GAAG,OACkC;yBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;;MATP;AAYN,kBAAkB,WAAW,GAAG;AAEhC,MAAM,oBAAoB;QAAC,EACzB,SAAS,EACT,GAAG,OACkC;yBACrC,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;MATP;AAYN,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,6JAAA,CAAA,aAAgB,OAGvC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,8KAAA,CAAA,QAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;;;AAGb,iBAAiB,WAAW,GAAG,8KAAA,CAAA,QAA0B,CAAC,WAAW;AAErE,MAAM,uCAAyB,6JAAA,CAAA,aAAgB,OAG7C,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,8KAAA,CAAA,cAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;;AAGb,uBAAuB,WAAW,GAChC,8KAAA,CAAA,cAAgC,CAAC,WAAW;AAE9C,MAAM,kCAAoB,6JAAA,CAAA,aAAgB,OAGxC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;;;AAGb,kBAAkB,WAAW,GAAG,8KAAA,CAAA,SAA2B,CAAC,WAAW;AAEvE,MAAM,kCAAoB,6JAAA,CAAA,aAAgB,QAGxC,QAA0B;QAAzB,EAAE,SAAS,EAAE,GAAG,OAAO;yBACxB,6LAAC,8KAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IACpC,gBACA;QAED,GAAG,KAAK;;;;;;;;AAGb,kBAAkB,WAAW,GAAG,8KAAA,CAAA,SAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 571, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/admin/BackupManager.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Badge } from '@/components/ui/badge'\nimport { Progress } from '@/components/ui/progress'\nimport {\n  Table,\n  TableBody,\n  TableCell,\n  TableHead,\n  TableHeader,\n  TableRow\n} from '@/components/ui/table'\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from '@/components/ui/dialog'\nimport {\n  AlertDialog,\n  AlertDialogAction,\n  AlertDialogCancel,\n  AlertDialogContent,\n  AlertDialogDescription,\n  AlertDialogFooter,\n  AlertDialogHeader,\n  AlertDialogTitle,\n} from '@/components/ui/alert-dialog'\nimport { \n  Database, \n  Download, \n  Trash2, \n  Refresh<PERSON><PERSON>, \n  Upload, \n  <PERSON><PERSON><PERSON><PERSON>gle,\n  CheckCircle,\n  Clock,\n  HardDrive\n} from 'lucide-react'\nimport { useToast } from '@/hooks/use-toast'\n\ninterface DatabaseBackup {\n  id: string\n  filename: string\n  originalName: string\n  fileSize: string // Changed from bigint to string for JSON serialization\n  backupType: string\n  status: string\n  filePath: string\n  createdAt: string\n  completedAt: string | null\n  errorMessage: string | null\n  description: string | null\n  user: {\n    firstName: string\n    lastName: string\n    email: string\n  }\n}\n\nexport default function BackupManager() {\n  const [backups, setBackups] = useState<DatabaseBackup[]>([])\n  const [loading, setLoading] = useState(true)\n  const [creating, setCreating] = useState(false)\n  const [restoring, setRestoring] = useState(false)\n  const [showCreateDialog, setShowCreateDialog] = useState(false)\n  const [showRestoreDialog, setShowRestoreDialog] = useState(false)\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false)\n  const [selectedBackup, setSelectedBackup] = useState<DatabaseBackup | null>(null)\n  const [description, setDescription] = useState('')\n  const { toast } = useToast()\n\n  useEffect(() => {\n    fetchBackups()\n    // Auto-refresh every 30 seconds to update backup status\n    const interval = setInterval(fetchBackups, 30000)\n    return () => clearInterval(interval)\n  }, [])\n\n  const fetchBackups = async () => {\n    try {\n      const response = await fetch('/api/admin/backups')\n      if (response.ok) {\n        const data = await response.json()\n        setBackups(data)\n      } else {\n        throw new Error('Failed to fetch backups')\n      }\n    } catch (error) {\n      console.error('Error fetching backups:', error)\n      toast({\n        title: 'Error',\n        description: 'Failed to load backups',\n        variant: 'destructive'\n      })\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const createBackup = async () => {\n    try {\n      setCreating(true)\n      const response = await fetch('/api/admin/backups', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          description: description.trim() || undefined,\n          backupType: 'MANUAL'\n        })\n      })\n\n      if (response.ok) {\n        const newBackup = await response.json()\n        setBackups(prev => [newBackup, ...prev])\n        setShowCreateDialog(false)\n        setDescription('')\n        toast({\n          title: 'Success',\n          description: 'Backup creation started. Please wait for completion.'\n        })\n      } else {\n        throw new Error('Failed to create backup')\n      }\n    } catch (error) {\n      console.error('Error creating backup:', error)\n      toast({\n        title: 'Error',\n        description: 'Failed to create backup',\n        variant: 'destructive'\n      })\n    } finally {\n      setCreating(false)\n    }\n  }\n\n  const downloadBackup = async (backup: DatabaseBackup) => {\n    try {\n      const response = await fetch(`/api/admin/backups/${backup.id}`)\n      if (response.ok) {\n        const blob = await response.blob()\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = backup.originalName\n        document.body.appendChild(a)\n        a.click()\n        window.URL.revokeObjectURL(url)\n        document.body.removeChild(a)\n        \n        toast({\n          title: 'Success',\n          description: 'Backup downloaded successfully'\n        })\n      } else {\n        throw new Error('Failed to download backup')\n      }\n    } catch (error) {\n      console.error('Error downloading backup:', error)\n      toast({\n        title: 'Error',\n        description: 'Failed to download backup',\n        variant: 'destructive'\n      })\n    }\n  }\n\n  const restoreBackup = async () => {\n    if (!selectedBackup) return\n\n    try {\n      setRestoring(true)\n      const response = await fetch(`/api/admin/backups/${selectedBackup.id}/restore`, {\n        method: 'POST'\n      })\n\n      if (response.ok) {\n        setShowRestoreDialog(false)\n        setSelectedBackup(null)\n        toast({\n          title: 'Success',\n          description: 'Database restored successfully. Please refresh the page.'\n        })\n      } else {\n        const error = await response.json()\n        throw new Error(error.details || 'Failed to restore backup')\n      }\n    } catch (error) {\n      console.error('Error restoring backup:', error)\n      toast({\n        title: 'Error',\n        description: error.message || 'Failed to restore backup',\n        variant: 'destructive'\n      })\n    } finally {\n      setRestoring(false)\n    }\n  }\n\n  const deleteBackup = async () => {\n    if (!selectedBackup) return\n\n    try {\n      const response = await fetch(`/api/admin/backups/${selectedBackup.id}`, {\n        method: 'DELETE'\n      })\n\n      if (response.ok) {\n        setBackups(prev => prev.filter(b => b.id !== selectedBackup.id))\n        setShowDeleteDialog(false)\n        setSelectedBackup(null)\n        toast({\n          title: 'Success',\n          description: 'Backup deleted successfully'\n        })\n      } else {\n        throw new Error('Failed to delete backup')\n      }\n    } catch (error) {\n      console.error('Error deleting backup:', error)\n      toast({\n        title: 'Error',\n        description: 'Failed to delete backup',\n        variant: 'destructive'\n      })\n    }\n  }\n\n  const getStatusBadge = (status: string) => {\n    switch (status) {\n      case 'COMPLETED':\n        return <Badge className=\"bg-green-100 text-green-800\"><CheckCircle className=\"h-3 w-3 mr-1\" />Completed</Badge>\n      case 'CREATING':\n        return <Badge className=\"bg-blue-100 text-blue-800\"><Clock className=\"h-3 w-3 mr-1\" />Creating</Badge>\n      case 'FAILED':\n        return <Badge className=\"bg-red-100 text-red-800\"><AlertTriangle className=\"h-3 w-3 mr-1\" />Failed</Badge>\n      default:\n        return <Badge variant=\"outline\">{status}</Badge>\n    }\n  }\n\n  const formatFileSize = (bytes: string) => {\n    const sizes = ['Bytes', 'KB', 'MB', 'GB']\n    const numBytes = parseInt(bytes)\n    if (numBytes === 0) return '0 Bytes'\n    const i = Math.floor(Math.log(numBytes) / Math.log(1024))\n    return Math.round(numBytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center gap-2\">\n            <Database className=\"h-5 w-5\" />\n            Database Backup Management\n          </CardTitle>\n          <CardDescription>\n            Create, manage, and restore database backups\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex justify-between items-center mb-6\">\n            <div className=\"flex items-center gap-2\">\n              <HardDrive className=\"h-4 w-4 text-gray-500\" />\n              <span className=\"text-sm text-gray-600\">\n                {backups.length} backup{backups.length !== 1 ? 's' : ''} available\n              </span>\n            </div>\n            <div className=\"flex gap-2\">\n              <Button\n                variant=\"outline\"\n                onClick={fetchBackups}\n                disabled={loading}\n              >\n                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />\n                Refresh\n              </Button>\n              <Button\n                onClick={() => setShowCreateDialog(true)}\n                disabled={creating}\n              >\n                <Database className=\"h-4 w-4 mr-2\" />\n                Create Backup\n              </Button>\n            </div>\n          </div>\n\n          {loading ? (\n            <div className=\"text-center py-8\">\n              <RefreshCw className=\"h-8 w-8 animate-spin mx-auto mb-4 text-gray-400\" />\n              <p className=\"text-gray-500\">Loading backups...</p>\n            </div>\n          ) : backups.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <Database className=\"h-12 w-12 mx-auto mb-4 text-gray-300\" />\n              <p className=\"text-gray-500 mb-2\">No backups found</p>\n              <p className=\"text-sm text-gray-400\">Create your first backup to get started</p>\n            </div>\n          ) : (\n            <Table>\n              <TableHeader>\n                <TableRow>\n                  <TableHead>Filename</TableHead>\n                  <TableHead>Status</TableHead>\n                  <TableHead>Size</TableHead>\n                  <TableHead>Created</TableHead>\n                  <TableHead>Created By</TableHead>\n                  <TableHead>Actions</TableHead>\n                </TableRow>\n              </TableHeader>\n              <TableBody>\n                {backups.map((backup) => (\n                  <TableRow key={backup.id}>\n                    <TableCell>\n                      <div>\n                        <div className=\"font-medium\">{backup.originalName}</div>\n                        {backup.description && (\n                          <div className=\"text-sm text-gray-500\">{backup.description}</div>\n                        )}\n                      </div>\n                    </TableCell>\n                    <TableCell>{getStatusBadge(backup.status)}</TableCell>\n                    <TableCell>{formatFileSize(backup.fileSize)}</TableCell>\n                    <TableCell>\n                      <div className=\"text-sm\">\n                        <div>{new Date(backup.createdAt).toLocaleDateString()}</div>\n                        <div className=\"text-gray-500\">\n                          {new Date(backup.createdAt).toLocaleTimeString()}\n                        </div>\n                      </div>\n                    </TableCell>\n                    <TableCell>\n                      <div className=\"text-sm\">\n                        <div>{backup.user.firstName} {backup.user.lastName}</div>\n                        <div className=\"text-gray-500\">{backup.user.email}</div>\n                      </div>\n                    </TableCell>\n                    <TableCell>\n                      <div className=\"flex gap-2\">\n                        {backup.status === 'COMPLETED' && (\n                          <>\n                            <Button\n                              size=\"sm\"\n                              variant=\"outline\"\n                              onClick={() => downloadBackup(backup)}\n                            >\n                              <Download className=\"h-3 w-3\" />\n                            </Button>\n                            <Button\n                              size=\"sm\"\n                              variant=\"outline\"\n                              onClick={() => {\n                                setSelectedBackup(backup)\n                                setShowRestoreDialog(true)\n                              }}\n                            >\n                              <Upload className=\"h-3 w-3\" />\n                            </Button>\n                          </>\n                        )}\n                        <Button\n                          size=\"sm\"\n                          variant=\"outline\"\n                          onClick={() => {\n                            setSelectedBackup(backup)\n                            setShowDeleteDialog(true)\n                          }}\n                        >\n                          <Trash2 className=\"h-3 w-3\" />\n                        </Button>\n                      </div>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Create Backup Dialog */}\n      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>\n        <DialogContent>\n          <DialogHeader>\n            <DialogTitle>Create Database Backup</DialogTitle>\n            <DialogDescription>\n              Create a complete backup of the database. This may take several minutes.\n            </DialogDescription>\n          </DialogHeader>\n          <div className=\"space-y-4\">\n            <div>\n              <Label htmlFor=\"description\">Description (Optional)</Label>\n              <Textarea\n                id=\"description\"\n                placeholder=\"Enter a description for this backup...\"\n                value={description}\n                onChange={(e) => setDescription(e.target.value)}\n                rows={3}\n              />\n            </div>\n          </div>\n          <DialogFooter>\n            <Button\n              variant=\"outline\"\n              onClick={() => setShowCreateDialog(false)}\n              disabled={creating}\n            >\n              Cancel\n            </Button>\n            <Button onClick={createBackup} disabled={creating}>\n              {creating ? (\n                <>\n                  <RefreshCw className=\"h-4 w-4 mr-2 animate-spin\" />\n                  Creating...\n                </>\n              ) : (\n                <>\n                  <Database className=\"h-4 w-4 mr-2\" />\n                  Create Backup\n                </>\n              )}\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* Restore Confirmation Dialog */}\n      <AlertDialog open={showRestoreDialog} onOpenChange={setShowRestoreDialog}>\n        <AlertDialogContent>\n          <AlertDialogHeader>\n            <AlertDialogTitle className=\"flex items-center gap-2\">\n              <AlertTriangle className=\"h-5 w-5 text-red-500\" />\n              Restore Database\n            </AlertDialogTitle>\n            <AlertDialogDescription asChild>\n              <div className=\"space-y-2\">\n                <div>\n                  <strong>WARNING:</strong> This will completely replace the current database with the backup data.\n                </div>\n                <div>All current data will be lost. This action cannot be undone.</div>\n                {selectedBackup && (\n                  <div className=\"mt-4 p-3 bg-gray-50 rounded\">\n                    <div><strong>Backup:</strong> {selectedBackup.originalName}</div>\n                    <div><strong>Created:</strong> {new Date(selectedBackup.createdAt).toLocaleString()}</div>\n                    <div><strong>Size:</strong> {formatFileSize(selectedBackup.fileSize)}</div>\n                  </div>\n                )}\n              </div>\n            </AlertDialogDescription>\n          </AlertDialogHeader>\n          <AlertDialogFooter>\n            <AlertDialogCancel disabled={restoring}>Cancel</AlertDialogCancel>\n            <AlertDialogAction\n              onClick={restoreBackup}\n              disabled={restoring}\n              className=\"bg-red-600 hover:bg-red-700\"\n            >\n              {restoring ? (\n                <>\n                  <RefreshCw className=\"h-4 w-4 mr-2 animate-spin\" />\n                  Restoring...\n                </>\n              ) : (\n                <>\n                  <Upload className=\"h-4 w-4 mr-2\" />\n                  Restore Database\n                </>\n              )}\n            </AlertDialogAction>\n          </AlertDialogFooter>\n        </AlertDialogContent>\n      </AlertDialog>\n\n      {/* Delete Confirmation Dialog */}\n      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>\n        <AlertDialogContent>\n          <AlertDialogHeader>\n            <AlertDialogTitle>Delete Backup</AlertDialogTitle>\n            <AlertDialogDescription>\n              Are you sure you want to delete this backup? This action cannot be undone.\n              {selectedBackup && (\n                <div className=\"mt-2 p-2 bg-gray-50 rounded\">\n                  <strong>{selectedBackup.originalName}</strong>\n                </div>\n              )}\n            </AlertDialogDescription>\n          </AlertDialogHeader>\n          <AlertDialogFooter>\n            <AlertDialogCancel>Cancel</AlertDialogCancel>\n            <AlertDialogAction\n              onClick={deleteBackup}\n              className=\"bg-red-600 hover:bg-red-700\"\n            >\n              <Trash2 className=\"h-4 w-4 mr-2\" />\n              Delete Backup\n            </AlertDialogAction>\n          </AlertDialogFooter>\n        </AlertDialogContent>\n      </AlertDialog>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AAEA;AAQA;AAQA;AAUA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AA/CA;;;;;;;;;;;;AAoEe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAC5E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,WAAQ,AAAD;IAEzB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;YACA,wDAAwD;YACxD,MAAM,WAAW,YAAY,cAAc;YAC3C;2CAAO,IAAM,cAAc;;QAC7B;kCAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,WAAW;YACb,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,YAAY;YACZ,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,aAAa,YAAY,IAAI,MAAM;oBACnC,YAAY;gBACd;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,WAAW,CAAA,OAAQ;wBAAC;2BAAc;qBAAK;gBACvC,oBAAoB;gBACpB,eAAe;gBACf,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,YAAY;QACd;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,sBAA+B,OAAV,OAAO,EAAE;YAC5D,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;gBACvC,MAAM,IAAI,SAAS,aAAa,CAAC;gBACjC,EAAE,IAAI,GAAG;gBACT,EAAE,QAAQ,GAAG,OAAO,YAAY;gBAChC,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,EAAE,KAAK;gBACP,OAAO,GAAG,CAAC,eAAe,CAAC;gBAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;gBAE1B,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,aAAa;YACb,MAAM,WAAW,MAAM,MAAM,AAAC,sBAAuC,OAAlB,eAAe,EAAE,EAAC,aAAW;gBAC9E,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,qBAAqB;gBACrB,kBAAkB;gBAClB,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;YACnC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;gBACJ,OAAO;gBACP,aAAa,MAAM,OAAO,IAAI;gBAC9B,SAAS;YACX;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,AAAC,sBAAuC,OAAlB,eAAe,EAAE,GAAI;gBACtE,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,eAAe,EAAE;gBAC9D,oBAAoB;gBACpB,kBAAkB;gBAClB,MAAM;oBACJ,OAAO;oBACP,aAAa;gBACf;YACF,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;;sCAA8B,6LAAC,8NAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;YAChG,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;;sCAA4B,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;YACxF,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;;sCAA0B,6LAAC,2NAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;YAC9F;gBACE,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAW;;;;;;QACrC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,WAAW,SAAS;QAC1B,IAAI,aAAa,GAAG,OAAO;QAC3B,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,YAAY,KAAK,GAAG,CAAC;QACnD,OAAO,KAAK,KAAK,CAAC,WAAW,KAAK,GAAG,CAAC,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,CAAC,EAAE;IAC9E;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGlC,6LAAC,mIAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,6LAAC,mIAAA,CAAA,cAAW;;0CACV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;gDAAK,WAAU;;oDACb,QAAQ,MAAM;oDAAC;oDAAQ,QAAQ,MAAM,KAAK,IAAI,MAAM;oDAAG;;;;;;;;;;;;;kDAG5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,SAAS;gDACT,UAAU;;kEAEV,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAW,AAAC,gBAA6C,OAA9B,UAAU,iBAAiB;;;;;;oDAAQ;;;;;;;0DAG3E,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS,IAAM,oBAAoB;gDACnC,UAAU;;kEAEV,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;4BAM1C,wBACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;uCAE7B,QAAQ,MAAM,KAAK,kBACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAClC,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;qDAGvC,6LAAC,oIAAA,CAAA,QAAK;;kDACJ,6LAAC,oIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,oIAAA,CAAA,WAAQ;;8DACP,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,6LAAC,oIAAA,CAAA,YAAS;8DAAC;;;;;;;;;;;;;;;;;kDAGf,6LAAC,oIAAA,CAAA,YAAS;kDACP,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,oIAAA,CAAA,WAAQ;;kEACP,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EAAe,OAAO,YAAY;;;;;;gEAChD,OAAO,WAAW,kBACjB,6LAAC;oEAAI,WAAU;8EAAyB,OAAO,WAAW;;;;;;;;;;;;;;;;;kEAIhE,6LAAC,oIAAA,CAAA,YAAS;kEAAE,eAAe,OAAO,MAAM;;;;;;kEACxC,6LAAC,oIAAA,CAAA,YAAS;kEAAE,eAAe,OAAO,QAAQ;;;;;;kEAC1C,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAK,IAAI,KAAK,OAAO,SAAS,EAAE,kBAAkB;;;;;;8EACnD,6LAAC;oEAAI,WAAU;8EACZ,IAAI,KAAK,OAAO,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;kEAIpD,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;wEAAK,OAAO,IAAI,CAAC,SAAS;wEAAC;wEAAE,OAAO,IAAI,CAAC,QAAQ;;;;;;;8EAClD,6LAAC;oEAAI,WAAU;8EAAiB,OAAO,IAAI,CAAC,KAAK;;;;;;;;;;;;;;;;;kEAGrD,6LAAC,oIAAA,CAAA,YAAS;kEACR,cAAA,6LAAC;4DAAI,WAAU;;gEACZ,OAAO,MAAM,KAAK,6BACjB;;sFACE,6LAAC,qIAAA,CAAA,SAAM;4EACL,MAAK;4EACL,SAAQ;4EACR,SAAS,IAAM,eAAe;sFAE9B,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;;;;;;sFAEtB,6LAAC,qIAAA,CAAA,SAAM;4EACL,MAAK;4EACL,SAAQ;4EACR,SAAS;gFACP,kBAAkB;gFAClB,qBAAqB;4EACvB;sFAEA,cAAA,6LAAC,yMAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;;;;;;;;8EAIxB,6LAAC,qIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,SAAS;wEACP,kBAAkB;wEAClB,oBAAoB;oEACtB;8EAEA,cAAA,6LAAC,6MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;+CAxDX,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAqEpC,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAkB,cAAc;0BAC5C,cAAA,6LAAC,qIAAA,CAAA,gBAAa;;sCACZ,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,6LAAC,qIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAIrB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;;kDACC,6LAAC,oIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAc;;;;;;kDAC7B,6LAAC,uIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,MAAM;;;;;;;;;;;;;;;;;sCAIZ,6LAAC,qIAAA,CAAA,eAAY;;8CACX,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,oBAAoB;oCACnC,UAAU;8CACX;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAc,UAAU;8CACtC,yBACC;;0DACE,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAA8B;;qEAIrD;;0DACE,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0BAUjD,6LAAC,8IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAmB,cAAc;0BAClD,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;sCACjB,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,mBAAgB;oCAAC,WAAU;;sDAC1B,6LAAC,2NAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;wCAAyB;;;;;;;8CAGpD,6LAAC,8IAAA,CAAA,yBAAsB;oCAAC,OAAO;8CAC7B,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;kEAAO;;;;;;oDAAiB;;;;;;;0DAE3B,6LAAC;0DAAI;;;;;;4CACJ,gCACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EAAI,6LAAC;0EAAO;;;;;;4DAAgB;4DAAE,eAAe,YAAY;;;;;;;kEAC1D,6LAAC;;0EAAI,6LAAC;0EAAO;;;;;;4DAAiB;4DAAE,IAAI,KAAK,eAAe,SAAS,EAAE,cAAc;;;;;;;kEACjF,6LAAC;;0EAAI,6LAAC;0EAAO;;;;;;4DAAc;4DAAE,eAAe,eAAe,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM7E,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,oBAAiB;oCAAC,UAAU;8CAAW;;;;;;8CACxC,6LAAC,8IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,0BACC;;0DACE,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAA8B;;qEAIrD;;0DACE,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0BAU/C,6LAAC,8IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAkB,cAAc;0BACjD,cAAA,6LAAC,8IAAA,CAAA,qBAAkB;;sCACjB,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,6LAAC,8IAAA,CAAA,yBAAsB;;wCAAC;wCAErB,gCACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;0DAAQ,eAAe,YAAY;;;;;;;;;;;;;;;;;;;;;;;sCAK5C,6LAAC,8IAAA,CAAA,oBAAiB;;8CAChB,6LAAC,8IAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,6LAAC,8IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,WAAU;;sDAEV,6LAAC,6MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD;GA5bwB;;QAUJ,+HAAA,CAAA,WAAQ;;;KAVJ", "debugId": null}}, {"offset": {"line": 1720, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/layout/PageHeader.tsx"], "sourcesContent": ["'use client'\n\nimport { useSession, signOut } from 'next-auth/react'\nimport { Button } from '@/components/ui/button'\nimport { ArrowLeft, LogOut, User, Home } from 'lucide-react'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { useState, useEffect } from 'react'\n\ninterface PageHeaderProps {\n  title: string\n  description?: string\n  showBackButton?: boolean\n  backUrl?: string\n  actions?: React.ReactNode\n  children?: React.ReactNode\n}\n\nexport default function PageHeader({\n  title,\n  description,\n  showBackButton = true,\n  backUrl = '/dashboard',\n  actions,\n  children\n}: PageHeaderProps) {\n  const { data: session } = useSession()\n  const router = useRouter()\n  const [companySettings, setCompanySettings] = useState({\n    systemTitle: 'Nilgala Micro',\n    companyLogo: ''\n  })\n\n  useEffect(() => {\n    fetchCompanySettings()\n  }, [])\n\n  const fetchCompanySettings = async () => {\n    try {\n      const response = await fetch('/api/company-settings')\n      if (response.ok) {\n        const data = await response.json()\n        setCompanySettings({\n          systemTitle: data.systemTitle || 'Nilgala Micro',\n          companyLogo: data.companyLogo || ''\n        })\n      }\n    } catch (error) {\n      console.error('Error fetching company settings:', error)\n    }\n  }\n\n  const handleBack = () => {\n    if (backUrl) {\n      router.push(backUrl)\n    } else {\n      router.back()\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Top Navigation Bar */}\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-3 sm:py-4\">\n            <div className=\"flex items-center space-x-2 sm:space-x-4 min-w-0 flex-1\">\n              <Link href=\"/dashboard\" className=\"flex items-center space-x-2 min-w-0\">\n                {companySettings.companyLogo ? (\n                  <img\n                    src={companySettings.companyLogo}\n                    alt=\"Company Logo\"\n                    className=\"h-6 sm:h-8 object-contain flex-shrink-0\"\n                    onError={(e) => {\n                      e.currentTarget.style.display = 'none'\n                    }}\n                  />\n                ) : null}\n                {!companySettings.companyLogo && (\n                  <h1 className=\"text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 truncate\">\n                    {companySettings.systemTitle}\n                  </h1>\n                )}\n              </Link>\n              {showBackButton && (\n                <div className=\"flex items-center space-x-1 sm:space-x-2\">\n                  <span className=\"text-gray-400 hidden sm:inline\">|</span>\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={handleBack}\n                    className=\"text-gray-600 hover:text-gray-900 px-2 sm:px-3\"\n                  >\n                    <ArrowLeft className=\"h-4 w-4 mr-1 sm:mr-2\" />\n                    <span className=\"hidden sm:inline\">Back</span>\n                  </Button>\n                </div>\n              )}\n            </div>\n\n            <div className=\"flex items-center space-x-2 sm:space-x-4 flex-shrink-0\">\n              <Link href=\"/dashboard\" className=\"hidden sm:block\">\n                <Button variant=\"ghost\" size=\"sm\">\n                  <Home className=\"h-4 w-4 mr-2\" />\n                  Dashboard\n                </Button>\n              </Link>\n\n              {/* Mobile Dashboard Link */}\n              <Link href=\"/dashboard\" className=\"sm:hidden\">\n                <Button variant=\"ghost\" size=\"sm\" className=\"px-2\">\n                  <Home className=\"h-4 w-4\" />\n                </Button>\n              </Link>\n\n              {session?.user && (\n                <>\n                  {/* Desktop User Info */}\n                  <div className=\"hidden lg:flex items-center space-x-2\">\n                    <User className=\"h-5 w-5 text-gray-500\" />\n                    <span className=\"text-sm text-gray-700\">\n                      {session.user.firstName} {session.user.lastName}\n                    </span>\n                    <span className=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\">\n                      {session.user.role.replace('_', ' ')}\n                    </span>\n                  </div>\n\n                  {/* Mobile User Info */}\n                  <div className=\"lg:hidden flex items-center space-x-1\">\n                    <User className=\"h-4 w-4 text-gray-500\" />\n                    <span className=\"text-xs bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded\">\n                      {session.user.role.replace('_', ' ').split(' ')[0]}\n                    </span>\n                  </div>\n\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => signOut({ callbackUrl: '/auth/signin' })}\n                    className=\"px-2 sm:px-3\"\n                  >\n                    <LogOut className=\"h-4 w-4 mr-0 sm:mr-2\" />\n                    <span className=\"hidden sm:inline\">Sign Out</span>\n                  </Button>\n                </>\n              )}\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Page Content */}\n      <main className=\"max-w-7xl mx-auto py-4 sm:py-6 px-4 sm:px-6 lg:px-8\">\n        <div className=\"py-4 sm:py-6\">\n          {/* Page Title Section */}\n          <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-4 mb-4 sm:mb-6\">\n            <div className=\"min-w-0 flex-1\">\n              <h1 className=\"text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 truncate\">{title}</h1>\n              {description && (\n                <p className=\"text-sm sm:text-base text-gray-600 mt-1\">{description}</p>\n              )}\n            </div>\n            {actions && (\n              <div className=\"flex items-center space-x-2 flex-shrink-0\">\n                {actions}\n              </div>\n            )}\n          </div>\n\n          {/* Page Content */}\n          {children}\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAPA;;;;;;;AAkBe,SAAS,WAAW,KAOjB;QAPiB,EACjC,KAAK,EACL,WAAW,EACX,iBAAiB,IAAI,EACrB,UAAU,YAAY,EACtB,OAAO,EACP,QAAQ,EACQ,GAPiB;;IAQjC,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACrD,aAAa;QACb,aAAa;IACf;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;QACF;+BAAG,EAAE;IAEL,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,mBAAmB;oBACjB,aAAa,KAAK,WAAW,IAAI;oBACjC,aAAa,KAAK,WAAW,IAAI;gBACnC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,SAAS;YACX,OAAO,IAAI,CAAC;QACd,OAAO;YACL,OAAO,IAAI;QACb;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;;4CAC/B,gBAAgB,WAAW,iBAC1B,6LAAC;gDACC,KAAK,gBAAgB,WAAW;gDAChC,KAAI;gDACJ,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;gDAClC;;;;;uDAEA;4CACH,CAAC,gBAAgB,WAAW,kBAC3B,6LAAC;gDAAG,WAAU;0DACX,gBAAgB,WAAW;;;;;;;;;;;;oCAIjC,gCACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAiC;;;;;;0DACjD,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,6LAAC,mNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,6LAAC;wDAAK,WAAU;kEAAmB;;;;;;;;;;;;;;;;;;;;;;;;0CAM3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAChC,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;;8DAC3B,6LAAC,sMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAMrC,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAChC,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;sDAC1C,cAAA,6LAAC,sMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;oCAInB,CAAA,oBAAA,8BAAA,QAAS,IAAI,mBACZ;;0DAEE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;;4DACb,QAAQ,IAAI,CAAC,SAAS;4DAAC;4DAAE,QAAQ,IAAI,CAAC,QAAQ;;;;;;;kEAEjD,6LAAC;wDAAK,WAAU;kEACb,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;0DAKpC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEACb,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;0DAItD,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;wDAAE,aAAa;oDAAe;gDACrD,WAAU;;kEAEV,6LAAC,6MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6LAAC;wDAAK,WAAU;kEAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUjD,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAoE;;;;;;wCACjF,6BACC,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;gCAG3D,yBACC,6LAAC;oCAAI,WAAU;8CACZ;;;;;;;;;;;;wBAMN;;;;;;;;;;;;;;;;;;AAKX;GA9JwB;;QAQI,iJAAA,CAAA,aAAU;QACrB,qIAAA,CAAA,YAAS;;;KATF", "debugId": null}}, {"offset": {"line": 2122, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/app/admin/backups/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\nimport BackupManager from '@/components/admin/BackupManager'\nimport PageHeader from '@/components/layout/PageHeader'\nimport { Database } from 'lucide-react'\n\nexport default function BackupsPage() {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (status === 'loading') return // Still loading\n\n    if (!session) {\n      router.push('/signin')\n      return\n    }\n\n    // Only Super Admin can access backup management\n    if (session.user.role !== 'SUPER_ADMIN') {\n      router.push('/dashboard')\n      return\n    }\n  }, [session, status, router])\n\n  // Show loading while checking authentication\n  if (status === 'loading') {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  // Show access denied if not Super Admin\n  if (!session || session.user.role !== 'SUPER_ADMIN') {\n    return (\n      <PageHeader\n        title=\"Access Denied\"\n        description=\"You don't have permission to access backup management.\"\n      >\n        <div className=\"text-center py-8\">\n          <Database className=\"h-16 w-16 mx-auto mb-4 text-gray-300\" />\n          <p className=\"text-gray-600 mb-4\">Only Super Administrators can manage database backups.</p>\n          <p className=\"text-sm text-gray-500\">Please contact your system administrator for access.</p>\n        </div>\n      </PageHeader>\n    )\n  }\n\n  return (\n    <PageHeader\n      title=\"Database Backup Management\"\n      description=\"Create, manage, and restore database backups\"\n    >\n      <BackupManager />\n    </PageHeader>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,WAAW,WAAW,QAAO,gBAAgB;YAEjD,IAAI,CAAC,SAAS;gBACZ,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,gDAAgD;YAChD,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,eAAe;gBACvC,OAAO,IAAI,CAAC;gBACZ;YACF;QACF;gCAAG;QAAC;QAAS;QAAQ;KAAO;IAE5B,6CAA6C;IAC7C,IAAI,WAAW,WAAW;QACxB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,wCAAwC;IACxC,IAAI,CAAC,WAAW,QAAQ,IAAI,CAAC,IAAI,KAAK,eAAe;QACnD,qBACE,6LAAC,6IAAA,CAAA,UAAU;YACT,OAAM;YACN,aAAY;sBAEZ,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAClC,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,qBACE,6LAAC,6IAAA,CAAA,UAAU;QACT,OAAM;QACN,aAAY;kBAEZ,cAAA,6LAAC,+IAAA,CAAA,UAAa;;;;;;;;;;AAGpB;GApDwB;;QACY,iJAAA,CAAA,aAAU;QAC7B,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}