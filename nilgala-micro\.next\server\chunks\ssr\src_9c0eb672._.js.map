{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,qMAAA,CAAA,aAAgB,CAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/components/layout/PageHeader.tsx"], "sourcesContent": ["'use client'\n\nimport { useSession, signOut } from 'next-auth/react'\nimport { Button } from '@/components/ui/button'\nimport { ArrowLeft, LogOut, User, Home } from 'lucide-react'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { useState, useEffect } from 'react'\n\ninterface PageHeaderProps {\n  title: string\n  description?: string\n  showBackButton?: boolean\n  backUrl?: string\n  actions?: React.ReactNode\n  children?: React.ReactNode\n}\n\nexport default function PageHeader({\n  title,\n  description,\n  showBackButton = true,\n  backUrl = '/dashboard',\n  actions,\n  children\n}: PageHeaderProps) {\n  const { data: session } = useSession()\n  const router = useRouter()\n  const [companySettings, setCompanySettings] = useState({\n    systemTitle: 'Nilgala Micro',\n    companyLogo: ''\n  })\n\n  useEffect(() => {\n    fetchCompanySettings()\n  }, [])\n\n  const fetchCompanySettings = async () => {\n    try {\n      const response = await fetch('/api/company-settings')\n      if (response.ok) {\n        const data = await response.json()\n        setCompanySettings({\n          systemTitle: data.systemTitle || 'Nilgala Micro',\n          companyLogo: data.companyLogo || ''\n        })\n      }\n    } catch (error) {\n      console.error('Error fetching company settings:', error)\n    }\n  }\n\n  const handleBack = () => {\n    if (backUrl) {\n      router.push(backUrl)\n    } else {\n      router.back()\n    }\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Top Navigation Bar */}\n      <header className=\"bg-white shadow\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-3 sm:py-4\">\n            <div className=\"flex items-center space-x-2 sm:space-x-4 min-w-0 flex-1\">\n              <Link href=\"/dashboard\" className=\"flex items-center space-x-2 min-w-0\">\n                {companySettings.companyLogo ? (\n                  <img\n                    src={companySettings.companyLogo}\n                    alt=\"Company Logo\"\n                    className=\"h-6 sm:h-8 object-contain flex-shrink-0\"\n                    onError={(e) => {\n                      e.currentTarget.style.display = 'none'\n                    }}\n                  />\n                ) : null}\n                {!companySettings.companyLogo && (\n                  <h1 className=\"text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 truncate\">\n                    {companySettings.systemTitle}\n                  </h1>\n                )}\n              </Link>\n              {showBackButton && (\n                <div className=\"flex items-center space-x-1 sm:space-x-2\">\n                  <span className=\"text-gray-400 hidden sm:inline\">|</span>\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={handleBack}\n                    className=\"text-gray-600 hover:text-gray-900 px-2 sm:px-3\"\n                  >\n                    <ArrowLeft className=\"h-4 w-4 mr-1 sm:mr-2\" />\n                    <span className=\"hidden sm:inline\">Back</span>\n                  </Button>\n                </div>\n              )}\n            </div>\n\n            <div className=\"flex items-center space-x-2 sm:space-x-4 flex-shrink-0\">\n              <Link href=\"/dashboard\" className=\"hidden sm:block\">\n                <Button variant=\"ghost\" size=\"sm\">\n                  <Home className=\"h-4 w-4 mr-2\" />\n                  Dashboard\n                </Button>\n              </Link>\n\n              {/* Mobile Dashboard Link */}\n              <Link href=\"/dashboard\" className=\"sm:hidden\">\n                <Button variant=\"ghost\" size=\"sm\" className=\"px-2\">\n                  <Home className=\"h-4 w-4\" />\n                </Button>\n              </Link>\n\n              {session?.user && (\n                <>\n                  {/* Desktop User Info */}\n                  <div className=\"hidden lg:flex items-center space-x-2\">\n                    <User className=\"h-5 w-5 text-gray-500\" />\n                    <span className=\"text-sm text-gray-700\">\n                      {session.user.firstName} {session.user.lastName}\n                    </span>\n                    <span className=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\">\n                      {session.user.role.replace('_', ' ')}\n                    </span>\n                  </div>\n\n                  {/* Mobile User Info */}\n                  <div className=\"lg:hidden flex items-center space-x-1\">\n                    <User className=\"h-4 w-4 text-gray-500\" />\n                    <span className=\"text-xs bg-blue-100 text-blue-800 px-1.5 py-0.5 rounded\">\n                      {session.user.role.replace('_', ' ').split(' ')[0]}\n                    </span>\n                  </div>\n\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => signOut({ callbackUrl: '/auth/signin' })}\n                    className=\"px-2 sm:px-3\"\n                  >\n                    <LogOut className=\"h-4 w-4 mr-0 sm:mr-2\" />\n                    <span className=\"hidden sm:inline\">Sign Out</span>\n                  </Button>\n                </>\n              )}\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Page Content */}\n      <main className=\"max-w-7xl mx-auto py-4 sm:py-6 px-4 sm:px-6 lg:px-8\">\n        <div className=\"py-4 sm:py-6\">\n          {/* Page Title Section */}\n          <div className=\"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-4 mb-4 sm:mb-6\">\n            <div className=\"min-w-0 flex-1\">\n              <h1 className=\"text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 truncate\">{title}</h1>\n              {description && (\n                <p className=\"text-sm sm:text-base text-gray-600 mt-1\">{description}</p>\n              )}\n            </div>\n            {actions && (\n              <div className=\"flex items-center space-x-2 flex-shrink-0\">\n                {actions}\n              </div>\n            )}\n          </div>\n\n          {/* Page Content */}\n          {children}\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AAkBe,SAAS,WAAW,EACjC,KAAK,EACL,WAAW,EACX,iBAAiB,IAAI,EACrB,UAAU,YAAY,EACtB,OAAO,EACP,QAAQ,EACQ;IAChB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrD,aAAa;QACb,aAAa;IACf;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,uBAAuB;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,mBAAmB;oBACjB,aAAa,KAAK,WAAW,IAAI;oBACjC,aAAa,KAAK,WAAW,IAAI;gBACnC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,SAAS;YACX,OAAO,IAAI,CAAC;QACd,OAAO;YACL,OAAO,IAAI;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;;4CAC/B,gBAAgB,WAAW,iBAC1B,8OAAC;gDACC,KAAK,gBAAgB,WAAW;gDAChC,KAAI;gDACJ,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;gDAClC;;;;;uDAEA;4CACH,CAAC,gBAAgB,WAAW,kBAC3B,8OAAC;gDAAG,WAAU;0DACX,gBAAgB,WAAW;;;;;;;;;;;;oCAIjC,gCACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAiC;;;;;;0DACjD,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;;kEAEV,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,8OAAC;wDAAK,WAAU;kEAAmB;;;;;;;;;;;;;;;;;;;;;;;;0CAM3C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAChC,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;;8DAC3B,8OAAC,mMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAMrC,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAChC,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;sDAC1C,cAAA,8OAAC,mMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;oCAInB,SAAS,sBACR;;0DAEE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAK,WAAU;;4DACb,QAAQ,IAAI,CAAC,SAAS;4DAAC;4DAAE,QAAQ,IAAI,CAAC,QAAQ;;;;;;;kEAEjD,8OAAC;wDAAK,WAAU;kEACb,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;0DAKpC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAK,WAAU;kEACb,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;0DAItD,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;wDAAE,aAAa;oDAAe;gDACrD,WAAU;;kEAEV,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;wDAAK,WAAU;kEAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUjD,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoE;;;;;;wCACjF,6BACC,8OAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;gCAG3D,yBACC,8OAAC;oCAAI,WAAU;8CACZ;;;;;;;;;;;;wBAMN;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 584, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/app/reports/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useSession } from 'next-auth/react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { useToast } from '@/hooks/use-toast'\n\nimport {\n  FileText,\n  Download,\n  Calendar,\n  TrendingUp,\n  Users,\n  DollarSign,\n  CreditCard,\n  AlertTriangle\n} from 'lucide-react'\nimport PageHeader from '@/components/layout/PageHeader'\nimport { formatCurrency } from '@/lib/utils'\nimport {\n  BarChart,\n  Bar,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  <PERSON>lt<PERSON>,\n  Legend,\n  Responsive<PERSON>ontainer,\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  <PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON>,\n  Area,\n  AreaChart\n} from 'recharts'\n\ninterface ReportData {\n  overview?: any\n  loans?: any\n  payments?: any\n  customers?: any\n  portfolio?: any\n  recentActivities?: any[]\n}\n\nconst COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8']\n\nconst LoadingCard = () => (\n  <Card>\n    <CardContent className=\"flex items-center justify-center h-64\">\n      <div className=\"text-center\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-2\"></div>\n        <p className=\"text-gray-600\">Loading...</p>\n      </div>\n    </CardContent>\n  </Card>\n)\n\nconst ErrorCard = ({ message }: { message: string }) => (\n  <Card>\n    <CardContent className=\"flex items-center justify-center h-64\">\n      <div className=\"text-center\">\n        <AlertTriangle className=\"h-8 w-8 text-red-500 mx-auto mb-2\" />\n        <p className=\"text-red-600\">{message}</p>\n      </div>\n    </CardContent>\n  </Card>\n)\n\nexport default function ReportsPage() {\n  const { data: session } = useSession()\n  const { toast } = useToast()\n  const [loading, setLoading] = useState(false)\n  const [reportData, setReportData] = useState<ReportData>({})\n  const [activeTab, setActiveTab] = useState('overview')\n  const [dateRange, setDateRange] = useState({\n    startDate: new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0],\n    endDate: new Date().toISOString().split('T')[0]\n  })\n\n  useEffect(() => {\n    fetchReportData()\n  }, [activeTab, dateRange])\n\n  const fetchReportData = async () => {\n    try {\n      setLoading(true)\n      const params = new URLSearchParams({\n        type: activeTab,\n        startDate: dateRange.startDate,\n        endDate: dateRange.endDate\n      })\n\n      const response = await fetch(`/api/reports?${params}`)\n      if (response.ok) {\n        const data = await response.json()\n        setReportData(prev => ({ ...prev, [activeTab]: data }))\n      } else {\n        toast({\n          title: 'Error',\n          description: 'Failed to fetch report data',\n          variant: 'destructive'\n        })\n      }\n    } catch (error) {\n      console.error('Error fetching report data:', error)\n      toast({\n        title: 'Error',\n        description: 'Failed to fetch report data',\n        variant: 'destructive'\n      })\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const exportReport = async (format: 'json' | 'csv' = 'json') => {\n    try {\n      const params = new URLSearchParams({\n        type: activeTab,\n        format,\n        startDate: dateRange.startDate,\n        endDate: dateRange.endDate\n      })\n\n      const response = await fetch(`/api/reports/export?${params}`)\n      if (response.ok) {\n        const blob = await response.blob()\n        const url = window.URL.createObjectURL(blob)\n        const a = document.createElement('a')\n        a.href = url\n        a.download = `${activeTab}-report-${dateRange.startDate}-to-${dateRange.endDate}.${format}`\n        document.body.appendChild(a)\n        a.click()\n        window.URL.revokeObjectURL(url)\n        document.body.removeChild(a)\n\n        toast({\n          title: 'Success',\n          description: `Report exported as ${format.toUpperCase()}`\n        })\n      }\n    } catch (error) {\n      console.error('Error exporting report:', error)\n      toast({\n        title: 'Error',\n        description: 'Failed to export report',\n        variant: 'destructive'\n      })\n    }\n  }\n\n  const renderOverviewReport = () => {\n    if (loading) return <LoadingCard />\n\n    const data = reportData.overview\n    if (!data) return <ErrorCard message=\"No overview data available\" />\n\n    try {\n      return (\n        <div className=\"space-y-6\">\n        {/* Key Metrics */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Total Loans</CardTitle>\n              <FileText className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{data.overview?.totalLoans || 0}</div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Total Customers</CardTitle>\n              <Users className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{data.overview?.totalCustomers || 0}</div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Total Disbursed</CardTitle>\n              <DollarSign className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">\n                {formatCurrency(data.overview?.totalDisbursed)}\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Total Collected</CardTitle>\n              <CreditCard className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">\n                {formatCurrency(data.overview?.totalCollected)}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Recent Activities */}\n        <Card>\n          <CardHeader>\n            <CardTitle>Recent Activities</CardTitle>\n            <CardDescription>Latest system activities and changes</CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              {data.recentActivities?.slice(0, 10).map((activity: any, index: number) => (\n                <div key={index} className=\"flex items-center space-x-4\">\n                  <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                  <div className=\"flex-1\">\n                    <p className=\"text-sm font-medium\">\n                      {activity.action} - {activity.resource}\n                      {activity.resourceId && ` (${activity.resourceId})`}\n                    </p>\n                    <p className=\"text-xs text-gray-500\">\n                      by {activity.user?.firstName} {activity.user?.lastName} • {' '}\n                      {new Date(activity.timestamp).toLocaleString()}\n                    </p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n        </div>\n      )\n    } catch (error) {\n      console.error('Error rendering overview report:', error)\n      return <ErrorCard message=\"Error loading overview report\" />\n    }\n  }\n\n  const renderLoansReport = () => {\n    if (loading) return <LoadingCard />\n\n    const data = reportData.loans\n    if (!data) return <ErrorCard message=\"No loan data available\" />\n\n    try {\n      // Prepare data for charts\n      const statusData = data.loansByStatus?.map((item: any) => ({\n        name: item.status,\n        count: item._count.id,\n        amount: Number(item._sum.principalAmount || 0)\n      })) || []\n\n      const disbursementData = data.disbursementTrend?.map((item: any) => ({\n        month: new Date(item.month).toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),\n        count: Number(item.count),\n        amount: Number(item.amount || 0)\n      })) || []\n\n      return (\n        <div className=\"space-y-6\">\n        {/* Key Metrics */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Average Loan Amount</CardTitle>\n              <DollarSign className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">\n                {formatCurrency(data.averageLoanAmount)}\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Total Loan Types</CardTitle>\n              <FileText className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{data.loansByType?.length || 0}</div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Disbursement Trend</CardTitle>\n              <TrendingUp className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{disbursementData.length} Months</div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Charts */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Loans by Status */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Loans by Status</CardTitle>\n              <CardDescription>Distribution of loans across different statuses</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <PieChart>\n                  <Pie\n                    data={statusData}\n                    cx=\"50%\"\n                    cy=\"50%\"\n                    labelLine={false}\n                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\n                    outerRadius={80}\n                    fill=\"#8884d8\"\n                    dataKey=\"count\"\n                  >\n                    {statusData.map((entry: any, index: number) => (\n                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />\n                    ))}\n                  </Pie>\n                  <Tooltip />\n                </PieChart>\n              </ResponsiveContainer>\n            </CardContent>\n          </Card>\n\n          {/* Disbursement Trend */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Disbursement Trend</CardTitle>\n              <CardDescription>Monthly loan disbursement amounts</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <AreaChart data={disbursementData}>\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"month\" />\n                  <YAxis />\n                  <Tooltip formatter={(value) => [formatCurrency(value), 'Amount']} />\n                  <Area type=\"monotone\" dataKey=\"amount\" stroke=\"#8884d8\" fill=\"#8884d8\" />\n                </AreaChart>\n              </ResponsiveContainer>\n            </CardContent>\n          </Card>\n        </div>\n        </div>\n      )\n    } catch (error) {\n      console.error('Error rendering loans report:', error)\n      return <ErrorCard message=\"Error loading loans report\" />\n    }\n  }\n\n  const renderPaymentsReport = () => {\n    if (loading) return <LoadingCard />\n\n    const data = reportData.payments\n    if (!data) return <ErrorCard message=\"No payment data available\" />\n\n    try {\n      // Prepare data for charts\n      const methodData = data.paymentsByMethod?.map((item: any) => ({\n        name: item.paymentMethod?.replace('_', ' ') || 'Unknown',\n        count: Number(item._count?.id || 0),\n        amount: Number(item._sum?.amount || 0)\n      })) || []\n\n      const trendData = data.paymentTrend?.map((item: any) => ({\n        day: item.day ? new Date(item.day).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }) : 'Unknown',\n        count: Number(item.count || 0),\n        amount: Number(item.amount || 0)\n      })) || []\n\n      return (\n        <div className=\"space-y-6\">\n        {/* Key Metrics */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Total Collections</CardTitle>\n              <DollarSign className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">\n                {formatCurrency(Number(data.totalCollections?.amount || 0))}\n              </div>\n              <p className=\"text-xs text-muted-foreground\">\n                {data.totalCollections?.count || 0} payments\n              </p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Average Payment</CardTitle>\n              <CreditCard className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">\n                {formatCurrency(Number(data.averagePayment || 0))}\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Payment Methods</CardTitle>\n              <FileText className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{methodData.length}</div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Charts */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Payments by Method */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Payments by Method</CardTitle>\n              <CardDescription>Distribution of payment methods</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <BarChart data={methodData}>\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"name\" />\n                  <YAxis />\n                  <Tooltip formatter={(value) => [formatCurrency(value), 'Amount']} />\n                  <Legend />\n                  <Bar dataKey=\"amount\" fill=\"#8884d8\" />\n                </BarChart>\n              </ResponsiveContainer>\n            </CardContent>\n          </Card>\n\n          {/* Payment Trend */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Daily Payment Trend</CardTitle>\n              <CardDescription>Daily payment collection amounts</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <LineChart data={trendData}>\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"day\" />\n                  <YAxis />\n                  <Tooltip formatter={(value) => [formatCurrency(value), 'Amount']} />\n                  <Line type=\"monotone\" dataKey=\"amount\" stroke=\"#8884d8\" strokeWidth={2} />\n                </LineChart>\n              </ResponsiveContainer>\n            </CardContent>\n          </Card>\n        </div>\n        </div>\n      )\n    } catch (error) {\n      console.error('Error rendering payments report:', error)\n      return <ErrorCard message=\"Error loading payments report\" />\n    }\n  }\n\n  const renderCustomersReport = () => {\n    if (loading) return <LoadingCard />\n\n    const data = reportData.customers\n    if (!data) return <ErrorCard message=\"No customer data available\" />\n\n    try {\n      // Prepare data for charts\n      const employmentData = data.customersByEmployment?.map((item: any) => ({\n        name: item.employmentType.replace('_', ' '),\n        count: item._count.id\n      })) || []\n\n      const cityData = data.customersByCity?.slice(0, 10).map((item: any) => ({\n        name: item.city,\n        count: item._count.id\n      })) || []\n\n      return (\n        <div className=\"space-y-6\">\n        {/* Key Metrics */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">New Customers</CardTitle>\n              <Users className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{data.newCustomers || 0}</div>\n              <p className=\"text-xs text-muted-foreground\">This period</p>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Average Income</CardTitle>\n              <DollarSign className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">\n                {formatCurrency(data.averageIncome)}\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Cities Served</CardTitle>\n              <FileText className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{cityData.length}</div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Charts */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Customers by Employment */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Customers by Employment Type</CardTitle>\n              <CardDescription>Distribution of customer employment types</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <PieChart>\n                  <Pie\n                    data={employmentData}\n                    cx=\"50%\"\n                    cy=\"50%\"\n                    labelLine={false}\n                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\n                    outerRadius={80}\n                    fill=\"#8884d8\"\n                    dataKey=\"count\"\n                  >\n                    {employmentData.map((entry: any, index: number) => (\n                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />\n                    ))}\n                  </Pie>\n                  <Tooltip />\n                </PieChart>\n              </ResponsiveContainer>\n            </CardContent>\n          </Card>\n\n          {/* Customers by City */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Top Cities</CardTitle>\n              <CardDescription>Customer distribution by city</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <BarChart data={cityData} layout=\"horizontal\">\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis type=\"number\" />\n                  <YAxis dataKey=\"name\" type=\"category\" width={80} />\n                  <Tooltip />\n                  <Bar dataKey=\"count\" fill=\"#8884d8\" />\n                </BarChart>\n              </ResponsiveContainer>\n            </CardContent>\n          </Card>\n        </div>\n        </div>\n      )\n    } catch (error) {\n      console.error('Error rendering customers report:', error)\n      return <ErrorCard message=\"Error loading customers report\" />\n    }\n  }\n\n  const renderPortfolioReport = () => {\n    if (loading) return <LoadingCard />\n\n    const data = reportData.portfolio\n    if (!data) return <ErrorCard message=\"No portfolio data available\" />\n\n    try {\n      // Prepare data for charts\n      const riskData = data.riskAnalysis?.map((item: any) => ({\n        name: item.status,\n        count: item._count.id,\n        amount: Number(item._sum.disbursedAmount || 0)\n      })) || []\n\n      // Calculate portfolio health metrics - API returns direct values\n      const totalPortfolio = Number(data.portfolioValue || 0)\n      const totalOutstanding = Number(data.outstandingAmount || 0)\n      const totalCollected = Number(data.collectionRate || 0)\n      const collectionRate = totalOutstanding > 0 ? (totalCollected / totalOutstanding) * 100 : 0\n\n      return (\n        <div className=\"space-y-6\">\n        {/* Key Metrics */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Portfolio Value</CardTitle>\n              <DollarSign className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">\n                {formatCurrency(totalPortfolio)}\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Outstanding Amount</CardTitle>\n              <AlertTriangle className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">\n                {formatCurrency(totalOutstanding)}\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Collection Rate</CardTitle>\n              <TrendingUp className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">\n                {collectionRate.toFixed(1)}%\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n              <CardTitle className=\"text-sm font-medium\">Risk Categories</CardTitle>\n              <FileText className=\"h-4 w-4 text-muted-foreground\" />\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-2xl font-bold\">{riskData.length}</div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Charts */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Risk Analysis */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Portfolio Risk Analysis</CardTitle>\n              <CardDescription>Loan distribution by status/risk level</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <PieChart>\n                  <Pie\n                    data={riskData}\n                    cx=\"50%\"\n                    cy=\"50%\"\n                    labelLine={false}\n                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\n                    outerRadius={80}\n                    fill=\"#8884d8\"\n                    dataKey=\"count\"\n                  >\n                    {riskData.map((entry: any, index: number) => (\n                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />\n                    ))}\n                  </Pie>\n                  <Tooltip />\n                </PieChart>\n              </ResponsiveContainer>\n            </CardContent>\n          </Card>\n\n          {/* Portfolio Performance */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Portfolio Performance</CardTitle>\n              <CardDescription>Amount distribution by loan status</CardDescription>\n            </CardHeader>\n            <CardContent>\n              <ResponsiveContainer width=\"100%\" height={300}>\n                <BarChart data={riskData}>\n                  <CartesianGrid strokeDasharray=\"3 3\" />\n                  <XAxis dataKey=\"name\" />\n                  <YAxis />\n                  <Tooltip formatter={(value) => [formatCurrency(value), 'Amount']} />\n                  <Bar dataKey=\"amount\" fill=\"#8884d8\" />\n                </BarChart>\n              </ResponsiveContainer>\n            </CardContent>\n          </Card>\n        </div>\n        </div>\n      )\n    } catch (error) {\n      console.error('Error rendering portfolio report:', error)\n      return <ErrorCard message=\"Error loading portfolio report\" />\n    }\n  }\n\n  if (!session) {\n    return (\n      <div className=\"flex items-center justify-center h-64\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-2\"></div>\n          <p className=\"text-gray-600\">Loading...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <PageHeader\n      title=\"System Reports\"\n      description=\"Comprehensive system analytics and reports\"\n      actions={\n        <div className=\"flex gap-2\">\n          <Button variant=\"outline\" onClick={() => exportReport('csv')}>\n            <Download className=\"h-4 w-4 mr-2\" />\n            Export CSV\n          </Button>\n          <Button onClick={() => exportReport('json')}>\n            <Download className=\"h-4 w-4 mr-2\" />\n            Export JSON\n          </Button>\n        </div>\n      }\n    >\n      <div className=\"space-y-6\">\n        {/* Date Range Filter */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-2\">\n              <Calendar className=\"h-5 w-5\" />\n              Report Period\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"flex gap-4\">\n              <div>\n                <Label htmlFor=\"startDate\">Start Date</Label>\n                <Input\n                  id=\"startDate\"\n                  type=\"date\"\n                  value={dateRange.startDate}\n                  onChange={(e) => setDateRange(prev => ({ ...prev, startDate: e.target.value }))}\n                />\n              </div>\n              <div>\n                <Label htmlFor=\"endDate\">End Date</Label>\n                <Input\n                  id=\"endDate\"\n                  type=\"date\"\n                  value={dateRange.endDate}\n                  onChange={(e) => setDateRange(prev => ({ ...prev, endDate: e.target.value }))}\n                />\n              </div>\n              <div className=\"flex items-end\">\n                <Button onClick={fetchReportData} disabled={loading}>\n                  {loading ? 'Loading...' : 'Update Report'}\n                </Button>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Report Tabs */}\n        <Tabs value={activeTab} onValueChange={setActiveTab}>\n          <TabsList className=\"grid w-full grid-cols-5\">\n            <TabsTrigger value=\"overview\">Overview</TabsTrigger>\n            <TabsTrigger value=\"loans\">Loans</TabsTrigger>\n            <TabsTrigger value=\"payments\">Payments</TabsTrigger>\n            <TabsTrigger value=\"customers\">Customers</TabsTrigger>\n            <TabsTrigger value=\"portfolio\">Portfolio</TabsTrigger>\n          </TabsList>\n\n          <TabsContent value=\"overview\" className=\"space-y-4\">\n            {renderOverviewReport()}\n          </TabsContent>\n\n          <TabsContent value=\"loans\" className=\"space-y-4\">\n            {renderLoansReport()}\n          </TabsContent>\n\n          <TabsContent value=\"payments\" className=\"space-y-4\">\n            {renderPaymentsReport()}\n          </TabsContent>\n\n          <TabsContent value=\"customers\" className=\"space-y-4\">\n            {renderCustomersReport()}\n          </TabsContent>\n\n          <TabsContent value=\"portfolio\" className=\"space-y-4\">\n            {renderPortfolioReport()}\n          </TabsContent>\n        </Tabs>\n      </div>\n    </PageHeader>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAxBA;;;;;;;;;;;;;;AAmDA,MAAM,SAAS;IAAC;IAAW;IAAW;IAAW;IAAW;CAAU;AAEtE,MAAM,cAAc,kBAClB,8OAAC,gIAAA,CAAA,OAAI;kBACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;;;;;AAMrC,MAAM,YAAY,CAAC,EAAE,OAAO,EAAuB,iBACjD,8OAAC,gIAAA,CAAA,OAAI;kBACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,wNAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;kCACzB,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;;;;;AAMtB,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IACnC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,CAAC;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,WAAW,IAAI,KAAK,IAAI,OAAO,WAAW,IAAI,IAAI,OAAO,QAAQ,IAAI,GAAG,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACnG,SAAS,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IACjD;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAW;KAAU;IAEzB,MAAM,kBAAkB;QACtB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI,gBAAgB;gBACjC,MAAM;gBACN,WAAW,UAAU,SAAS;gBAC9B,SAAS,UAAU,OAAO;YAC5B;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,aAAa,EAAE,QAAQ;YACrD,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,cAAc,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,CAAC,UAAU,EAAE;oBAAK,CAAC;YACvD,OAAO;gBACL,MAAM;oBACJ,OAAO;oBACP,aAAa;oBACb,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe,OAAO,SAAyB,MAAM;QACzD,IAAI;YACF,MAAM,SAAS,IAAI,gBAAgB;gBACjC,MAAM;gBACN;gBACA,WAAW,UAAU,SAAS;gBAC9B,SAAS,UAAU,OAAO;YAC5B;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,oBAAoB,EAAE,QAAQ;YAC5D,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;gBACvC,MAAM,IAAI,SAAS,aAAa,CAAC;gBACjC,EAAE,IAAI,GAAG;gBACT,EAAE,QAAQ,GAAG,GAAG,UAAU,QAAQ,EAAE,UAAU,SAAS,CAAC,IAAI,EAAE,UAAU,OAAO,CAAC,CAAC,EAAE,QAAQ;gBAC3F,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,EAAE,KAAK;gBACP,OAAO,GAAG,CAAC,eAAe,CAAC;gBAC3B,SAAS,IAAI,CAAC,WAAW,CAAC;gBAE1B,MAAM;oBACJ,OAAO;oBACP,aAAa,CAAC,mBAAmB,EAAE,OAAO,WAAW,IAAI;gBAC3D;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,SAAS;YACX;QACF;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,SAAS,qBAAO,8OAAC;;;;;QAErB,MAAM,OAAO,WAAW,QAAQ;QAChC,IAAI,CAAC,MAAM,qBAAO,8OAAC;YAAU,SAAQ;;;;;;QAErC,IAAI;YACF,qBACE,8OAAC;gBAAI,WAAU;;kCAEf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;kDAEtB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDAAsB,KAAK,QAAQ,EAAE,cAAc;;;;;;;;;;;;;;;;;0CAItE,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;kDAEnB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDAAsB,KAAK,QAAQ,EAAE,kBAAkB;;;;;;;;;;;;;;;;;0CAI1E,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,QAAQ,EAAE;;;;;;;;;;;;;;;;;0CAKrC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;kCAOvC,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAEnB,8OAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,8OAAC;oCAAI,WAAU;8CACZ,KAAK,gBAAgB,EAAE,MAAM,GAAG,IAAI,IAAI,CAAC,UAAe,sBACvD,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;;gEACV,SAAS,MAAM;gEAAC;gEAAI,SAAS,QAAQ;gEACrC,SAAS,UAAU,IAAI,CAAC,EAAE,EAAE,SAAS,UAAU,CAAC,CAAC,CAAC;;;;;;;sEAErD,8OAAC;4DAAE,WAAU;;gEAAwB;gEAC/B,SAAS,IAAI,EAAE;gEAAU;gEAAE,SAAS,IAAI,EAAE;gEAAS;gEAAI;gEAC1D,IAAI,KAAK,SAAS,SAAS,EAAE,cAAc;;;;;;;;;;;;;;2CATxC;;;;;;;;;;;;;;;;;;;;;;;;;;;QAmBtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,qBAAO,8OAAC;gBAAU,SAAQ;;;;;;QAC5B;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAI,SAAS,qBAAO,8OAAC;;;;;QAErB,MAAM,OAAO,WAAW,KAAK;QAC7B,IAAI,CAAC,MAAM,qBAAO,8OAAC;YAAU,SAAQ;;;;;;QAErC,IAAI;YACF,0BAA0B;YAC1B,MAAM,aAAa,KAAK,aAAa,EAAE,IAAI,CAAC,OAAc,CAAC;oBACzD,MAAM,KAAK,MAAM;oBACjB,OAAO,KAAK,MAAM,CAAC,EAAE;oBACrB,QAAQ,OAAO,KAAK,IAAI,CAAC,eAAe,IAAI;gBAC9C,CAAC,MAAM,EAAE;YAET,MAAM,mBAAmB,KAAK,iBAAiB,EAAE,IAAI,CAAC,OAAc,CAAC;oBACnE,OAAO,IAAI,KAAK,KAAK,KAAK,EAAE,kBAAkB,CAAC,SAAS;wBAAE,OAAO;wBAAS,MAAM;oBAAU;oBAC1F,OAAO,OAAO,KAAK,KAAK;oBACxB,QAAQ,OAAO,KAAK,MAAM,IAAI;gBAChC,CAAC,MAAM,EAAE;YAET,qBACE,8OAAC;gBAAI,WAAU;;kCAEf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,iBAAiB;;;;;;;;;;;;;;;;;0CAK5C,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;kDAEtB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDAAsB,KAAK,WAAW,EAAE,UAAU;;;;;;;;;;;;;;;;;0CAIrE,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;gDAAsB,iBAAiB,MAAM;gDAAC;;;;;;;;;;;;;;;;;;;;;;;;kCAMnE,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;4CAAC,OAAM;4CAAO,QAAQ;sDACxC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;;kEACP,8OAAC,+IAAA,CAAA,MAAG;wDACF,MAAM;wDACN,IAAG;wDACH,IAAG;wDACH,WAAW;wDACX,OAAO,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,GAAK,GAAG,KAAK,CAAC,EAAE,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;wDACtE,aAAa;wDACb,MAAK;wDACL,SAAQ;kEAEP,WAAW,GAAG,CAAC,CAAC,OAAY,sBAC3B,8OAAC,oJAAA,CAAA,OAAI;gEAAuB,MAAM,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;+DAApD,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;kEAG9B,8OAAC,uJAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhB,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;4CAAC,OAAM;4CAAO,QAAQ;sDACxC,cAAA,8OAAC,qJAAA,CAAA,YAAS;gDAAC,MAAM;;kEACf,8OAAC,6JAAA,CAAA,gBAAa;wDAAC,iBAAgB;;;;;;kEAC/B,8OAAC,qJAAA,CAAA,QAAK;wDAAC,SAAQ;;;;;;kEACf,8OAAC,qJAAA,CAAA,QAAK;;;;;kEACN,8OAAC,uJAAA,CAAA,UAAO;wDAAC,WAAW,CAAC,QAAU;gEAAC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;gEAAQ;6DAAS;;;;;;kEAChE,8OAAC,oJAAA,CAAA,OAAI;wDAAC,MAAK;wDAAW,SAAQ;wDAAS,QAAO;wDAAU,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAQ3E,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,qBAAO,8OAAC;gBAAU,SAAQ;;;;;;QAC5B;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,SAAS,qBAAO,8OAAC;;;;;QAErB,MAAM,OAAO,WAAW,QAAQ;QAChC,IAAI,CAAC,MAAM,qBAAO,8OAAC;YAAU,SAAQ;;;;;;QAErC,IAAI;YACF,0BAA0B;YAC1B,MAAM,aAAa,KAAK,gBAAgB,EAAE,IAAI,CAAC,OAAc,CAAC;oBAC5D,MAAM,KAAK,aAAa,EAAE,QAAQ,KAAK,QAAQ;oBAC/C,OAAO,OAAO,KAAK,MAAM,EAAE,MAAM;oBACjC,QAAQ,OAAO,KAAK,IAAI,EAAE,UAAU;gBACtC,CAAC,MAAM,EAAE;YAET,MAAM,YAAY,KAAK,YAAY,EAAE,IAAI,CAAC,OAAc,CAAC;oBACvD,KAAK,KAAK,GAAG,GAAG,IAAI,KAAK,KAAK,GAAG,EAAE,kBAAkB,CAAC,SAAS;wBAAE,OAAO;wBAAS,KAAK;oBAAU,KAAK;oBACrG,OAAO,OAAO,KAAK,KAAK,IAAI;oBAC5B,QAAQ,OAAO,KAAK,MAAM,IAAI;gBAChC,CAAC,MAAM,EAAE;YAET,qBACE,8OAAC;gBAAI,WAAU;;kCAEf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAI,WAAU;0DACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,KAAK,gBAAgB,EAAE,UAAU;;;;;;0DAE1D,8OAAC;gDAAE,WAAU;;oDACV,KAAK,gBAAgB,EAAE,SAAS;oDAAE;;;;;;;;;;;;;;;;;;;0CAKzC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,KAAK,cAAc,IAAI;;;;;;;;;;;;;;;;;0CAKpD,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;kDAEtB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDAAsB,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAM5D,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;4CAAC,OAAM;4CAAO,QAAQ;sDACxC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;gDAAC,MAAM;;kEACd,8OAAC,6JAAA,CAAA,gBAAa;wDAAC,iBAAgB;;;;;;kEAC/B,8OAAC,qJAAA,CAAA,QAAK;wDAAC,SAAQ;;;;;;kEACf,8OAAC,qJAAA,CAAA,QAAK;;;;;kEACN,8OAAC,uJAAA,CAAA,UAAO;wDAAC,WAAW,CAAC,QAAU;gEAAC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;gEAAQ;6DAAS;;;;;;kEAChE,8OAAC,sJAAA,CAAA,SAAM;;;;;kEACP,8OAAC,mJAAA,CAAA,MAAG;wDAAC,SAAQ;wDAAS,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOnC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;4CAAC,OAAM;4CAAO,QAAQ;sDACxC,cAAA,8OAAC,qJAAA,CAAA,YAAS;gDAAC,MAAM;;kEACf,8OAAC,6JAAA,CAAA,gBAAa;wDAAC,iBAAgB;;;;;;kEAC/B,8OAAC,qJAAA,CAAA,QAAK;wDAAC,SAAQ;;;;;;kEACf,8OAAC,qJAAA,CAAA,QAAK;;;;;kEACN,8OAAC,uJAAA,CAAA,UAAO;wDAAC,WAAW,CAAC,QAAU;gEAAC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;gEAAQ;6DAAS;;;;;;kEAChE,8OAAC,oJAAA,CAAA,OAAI;wDAAC,MAAK;wDAAW,SAAQ;wDAAS,QAAO;wDAAU,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAQnF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,qBAAO,8OAAC;gBAAU,SAAQ;;;;;;QAC5B;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,SAAS,qBAAO,8OAAC;;;;;QAErB,MAAM,OAAO,WAAW,SAAS;QACjC,IAAI,CAAC,MAAM,qBAAO,8OAAC;YAAU,SAAQ;;;;;;QAErC,IAAI;YACF,0BAA0B;YAC1B,MAAM,iBAAiB,KAAK,qBAAqB,EAAE,IAAI,CAAC,OAAc,CAAC;oBACrE,MAAM,KAAK,cAAc,CAAC,OAAO,CAAC,KAAK;oBACvC,OAAO,KAAK,MAAM,CAAC,EAAE;gBACvB,CAAC,MAAM,EAAE;YAET,MAAM,WAAW,KAAK,eAAe,EAAE,MAAM,GAAG,IAAI,IAAI,CAAC,OAAc,CAAC;oBACtE,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,MAAM,CAAC,EAAE;gBACvB,CAAC,MAAM,EAAE;YAET,qBACE,8OAAC;gBAAI,WAAU;;kCAEf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;kDAEnB,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAI,WAAU;0DAAsB,KAAK,YAAY,IAAI;;;;;;0DAC1D,8OAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CAIjD,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,aAAa;;;;;;;;;;;;;;;;;0CAKxC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;kDAEtB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDAAsB,SAAS,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAM1D,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;4CAAC,OAAM;4CAAO,QAAQ;sDACxC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;;kEACP,8OAAC,+IAAA,CAAA,MAAG;wDACF,MAAM;wDACN,IAAG;wDACH,IAAG;wDACH,WAAW;wDACX,OAAO,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,GAAK,GAAG,KAAK,CAAC,EAAE,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;wDACtE,aAAa;wDACb,MAAK;wDACL,SAAQ;kEAEP,eAAe,GAAG,CAAC,CAAC,OAAY,sBAC/B,8OAAC,oJAAA,CAAA,OAAI;gEAAuB,MAAM,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;+DAApD,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;kEAG9B,8OAAC,uJAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhB,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;4CAAC,OAAM;4CAAO,QAAQ;sDACxC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;gDAAC,MAAM;gDAAU,QAAO;;kEAC/B,8OAAC,6JAAA,CAAA,gBAAa;wDAAC,iBAAgB;;;;;;kEAC/B,8OAAC,qJAAA,CAAA,QAAK;wDAAC,MAAK;;;;;;kEACZ,8OAAC,qJAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAO,MAAK;wDAAW,OAAO;;;;;;kEAC7C,8OAAC,uJAAA,CAAA,UAAO;;;;;kEACR,8OAAC,mJAAA,CAAA,MAAG;wDAAC,SAAQ;wDAAQ,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAQxC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,qBAAO,8OAAC;gBAAU,SAAQ;;;;;;QAC5B;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,SAAS,qBAAO,8OAAC;;;;;QAErB,MAAM,OAAO,WAAW,SAAS;QACjC,IAAI,CAAC,MAAM,qBAAO,8OAAC;YAAU,SAAQ;;;;;;QAErC,IAAI;YACF,0BAA0B;YAC1B,MAAM,WAAW,KAAK,YAAY,EAAE,IAAI,CAAC,OAAc,CAAC;oBACtD,MAAM,KAAK,MAAM;oBACjB,OAAO,KAAK,MAAM,CAAC,EAAE;oBACrB,QAAQ,OAAO,KAAK,IAAI,CAAC,eAAe,IAAI;gBAC9C,CAAC,MAAM,EAAE;YAET,iEAAiE;YACjE,MAAM,iBAAiB,OAAO,KAAK,cAAc,IAAI;YACrD,MAAM,mBAAmB,OAAO,KAAK,iBAAiB,IAAI;YAC1D,MAAM,iBAAiB,OAAO,KAAK,cAAc,IAAI;YACrD,MAAM,iBAAiB,mBAAmB,IAAI,AAAC,iBAAiB,mBAAoB,MAAM;YAE1F,qBACE,8OAAC;gBAAI,WAAU;;kCAEf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;;;;;0CAKtB,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;;kDAE3B,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDACZ,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;;;;;;;;;;;;;;;;;0CAKtB,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;;gDACZ,eAAe,OAAO,CAAC;gDAAG;;;;;;;;;;;;;;;;;;0CAKjC,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAsB;;;;;;0DAC3C,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;kDAEtB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDAAsB,SAAS,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAM1D,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;4CAAC,OAAM;4CAAO,QAAQ;sDACxC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;;kEACP,8OAAC,+IAAA,CAAA,MAAG;wDACF,MAAM;wDACN,IAAG;wDACH,IAAG;wDACH,WAAW;wDACX,OAAO,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,GAAK,GAAG,KAAK,CAAC,EAAE,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;wDACtE,aAAa;wDACb,MAAK;wDACL,SAAQ;kEAEP,SAAS,GAAG,CAAC,CAAC,OAAY,sBACzB,8OAAC,oJAAA,CAAA,OAAI;gEAAuB,MAAM,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;+DAApD,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;kEAG9B,8OAAC,uJAAA,CAAA,UAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhB,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;;0DACT,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAEnB,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC,mKAAA,CAAA,sBAAmB;4CAAC,OAAM;4CAAO,QAAQ;sDACxC,cAAA,8OAAC,oJAAA,CAAA,WAAQ;gDAAC,MAAM;;kEACd,8OAAC,6JAAA,CAAA,gBAAa;wDAAC,iBAAgB;;;;;;kEAC/B,8OAAC,qJAAA,CAAA,QAAK;wDAAC,SAAQ;;;;;;kEACf,8OAAC,qJAAA,CAAA,QAAK;;;;;kEACN,8OAAC,uJAAA,CAAA,UAAO;wDAAC,WAAW,CAAC,QAAU;gEAAC,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE;gEAAQ;6DAAS;;;;;;kEAChE,8OAAC,mJAAA,CAAA,MAAG;wDAAC,SAAQ;wDAAS,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAQzC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,qBAAO,8OAAC;gBAAU,SAAQ;;;;;;QAC5B;IACF;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,8OAAC,0IAAA,CAAA,UAAU;QACT,OAAM;QACN,aAAY;QACZ,uBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,SAAS,IAAM,aAAa;;sCACpD,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;8BAGvC,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAS,IAAM,aAAa;;sCAClC,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;kBAM3C,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,gIAAA,CAAA,OAAI;;sCACH,8OAAC,gIAAA,CAAA,aAAU;sCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;sCAIpC,8OAAC,gIAAA,CAAA,cAAW;sCACV,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAY;;;;;;0DAC3B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,UAAU,SAAS;gDAC1B,UAAU,CAAC,IAAM,aAAa,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;;;;;;;;;;;;kDAGjF,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;0DAAU;;;;;;0DACzB,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,UAAU,OAAO;gDACxB,UAAU,CAAC,IAAM,aAAa,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,SAAS,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;;;;;;;;;;;;kDAG/E,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAS;4CAAiB,UAAU;sDACzC,UAAU,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQpC,8OAAC,gIAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe;;sCACrC,8OAAC,gIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAW;;;;;;8CAC9B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAQ;;;;;;8CAC3B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAW;;;;;;8CAC9B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAY;;;;;;8CAC/B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAY;;;;;;;;;;;;sCAGjC,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACrC;;;;;;sCAGH,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAQ,WAAU;sCAClC;;;;;;sCAGH,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACrC;;;;;;sCAGH,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAY,WAAU;sCACtC;;;;;;sCAGH,8OAAC,gIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAY,WAAU;sCACtC;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}]}