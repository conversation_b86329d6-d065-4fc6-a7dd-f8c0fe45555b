module.exports = {

"[project]/.next-internal/server/app/api/loans/[id]/disburse/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "prisma": ()=>prisma
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "authOptions": ()=>authOptions,
    "checkPermissions": ()=>checkPermissions,
    "checkPermissionsSync": ()=>checkPermissionsSync,
    "clearPermissionsCache": ()=>clearPermissionsCache,
    "getRolePermissions": ()=>getRolePermissions,
    "hasPermission": ()=>hasPermission,
    "hasPermissionSync": ()=>hasPermissionSync,
    "initializePermissionsCache": ()=>initializePermissionsCache
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$auth$2f$prisma$2d$adapter$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@auth/prisma-adapter/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
;
;
;
;
// Cache for role permissions to avoid database calls on every request
let rolePermissionsCache = {};
let cacheLastUpdated = 0;
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes
;
const authOptions = {
    adapter: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$auth$2f$prisma$2d$adapter$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PrismaAdapter"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"]),
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            name: 'credentials',
            credentials: {
                email: {
                    label: 'Email',
                    type: 'email'
                },
                password: {
                    label: 'Password',
                    type: 'password'
                }
            },
            async authorize (credentials) {
                if (!credentials?.email || !credentials?.password) {
                    return null;
                }
                const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
                    where: {
                        email: credentials.email
                    }
                });
                if (!user || !user.isActive) {
                    return null;
                }
                const isPasswordValid = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(credentials.password, user.password);
                if (!isPasswordValid) {
                    return null;
                }
                // Update last login
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.update({
                    where: {
                        id: user.id
                    },
                    data: {
                        lastLogin: new Date()
                    }
                });
                return {
                    id: user.id,
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    role: user.role,
                    avatar: user.avatar || undefined
                };
            }
        })
    ],
    session: {
        strategy: 'jwt'
    },
    callbacks: {
        async jwt ({ token, user }) {
            if (user) {
                token.role = user.role;
                token.firstName = user.firstName;
                token.lastName = user.lastName;
            }
            return token;
        },
        async session ({ session, token }) {
            if (token) {
                session.user.id = token.sub;
                session.user.role = token.role;
                session.user.firstName = token.firstName;
                session.user.lastName = token.lastName;
            }
            return session;
        }
    },
    pages: {
        signIn: '/auth/signin',
        error: '/auth/error'
    }
};
// Load role permissions from database with caching
async function loadRolePermissions() {
    const now = Date.now();
    // Return cached permissions if still valid
    if (cacheLastUpdated && now - cacheLastUpdated < CACHE_DURATION) {
        return rolePermissionsCache;
    }
    try {
        // Fetch all active role permissions from database
        const permissions = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].rolePermission.findMany({
            where: {
                isActive: true
            },
            select: {
                role: true,
                permission: true
            }
        });
        // Group permissions by role
        const rolePermissions = {};
        for (const perm of permissions){
            if (!rolePermissions[perm.role]) {
                rolePermissions[perm.role] = [];
            }
            rolePermissions[perm.role].push(perm.permission);
        }
        // Update cache
        rolePermissionsCache = rolePermissions;
        cacheLastUpdated = now;
        return rolePermissions;
    } catch (error) {
        console.error('Error loading role permissions from database:', error);
        // Return empty permissions on error to be safe
        return {};
    }
}
async function getRolePermissions() {
    return await loadRolePermissions();
}
function clearPermissionsCache() {
    rolePermissionsCache = {};
    cacheLastUpdated = 0;
}
async function initializePermissionsCache() {
    try {
        await loadRolePermissions();
        console.log('✅ Permissions cache initialized');
    } catch (error) {
        console.error('❌ Failed to initialize permissions cache:', error);
    }
}
async function hasPermission(userRole, permission) {
    const rolePermissions = await getRolePermissions();
    return rolePermissions[userRole]?.includes(permission) || false;
}
async function checkPermissions(userRole, requiredPermissions) {
    const rolePermissions = await getRolePermissions();
    return requiredPermissions.every((permission)=>rolePermissions[userRole]?.includes(permission) || false);
}
function hasPermissionSync(userRole, permission) {
    return rolePermissionsCache[userRole]?.includes(permission) || false;
}
function checkPermissionsSync(userRole, requiredPermissions) {
    return requiredPermissions.every((permission)=>hasPermissionSync(userRole, permission));
}
}),
"[project]/src/lib/customer-status.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "getCustomerStatusInfo": ()=>getCustomerStatusInfo,
    "getStatusBadgeColor": ()=>getStatusBadgeColor,
    "getStatusLabel": ()=>getStatusLabel,
    "updateCustomerStatus": ()=>updateCustomerStatus,
    "validateStatusChange": ()=>validateStatusChange
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
;
async function updateCustomerStatus(customerId, updatedBy) {
    try {
        // Get customer with their loans
        const customer = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].customer.findUnique({
            where: {
                id: customerId
            },
            include: {
                loans: {
                    where: {
                        status: {
                            in: [
                                'ACTIVE',
                                'DISBURSED'
                            ]
                        }
                    }
                }
            }
        });
        if (!customer) {
            throw new Error('Customer not found');
        }
        // Don't auto-update if customer is manually suspended or blacklisted
        if (customer.status === 'SUSPENDED' || customer.status === 'BLACKLISTED') {
            return customer.status;
        }
        // Determine new status based on active loans
        const newStatus = customer.loans.length > 0 ? 'ACTIVE' : 'INACTIVE';
        // Only update if status has changed
        if (customer.status !== newStatus) {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].customer.update({
                where: {
                    id: customerId
                },
                data: {
                    status: newStatus,
                    statusUpdatedBy: updatedBy || null,
                    statusUpdatedAt: new Date(),
                    statusNotes: `Auto-updated based on loan activity`
                }
            });
            // Create audit log for automatic status updates
            if (updatedBy) {
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].auditLog.create({
                    data: {
                        action: 'AUTO_UPDATE_STATUS',
                        resource: 'Customer',
                        resourceId: customerId,
                        userId: updatedBy,
                        oldValues: {
                            status: customer.status
                        },
                        newValues: {
                            status: newStatus,
                            reason: 'Automatic update based on loan activity'
                        }
                    }
                });
            }
        }
        return newStatus;
    } catch (error) {
        console.error('Error updating customer status:', error);
        throw error;
    }
}
async function getCustomerStatusInfo(customerId) {
    const customer = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].customer.findUnique({
        where: {
            id: customerId
        },
        include: {
            loans: {
                where: {
                    status: {
                        in: [
                            'ACTIVE',
                            'DISBURSED'
                        ]
                    }
                },
                select: {
                    id: true,
                    loanNumber: true,
                    status: true,
                    principalAmount: true,
                    disbursedAmount: true
                }
            }
        }
    });
    if (!customer) {
        return null;
    }
    const statusInfo = {
        currentStatus: customer.status,
        activeLoansCount: customer.loans.length,
        totalActiveAmount: customer.loans.reduce((sum, loan)=>sum + Number(loan.disbursedAmount || loan.principalAmount), 0),
        canBeSetToActive: customer.loans.length > 0,
        lastStatusUpdate: customer.statusUpdatedAt,
        statusNotes: customer.statusNotes
    };
    return {
        customer,
        statusInfo
    };
}
function validateStatusChange(currentStatus, newStatus, userRole, hasActiveLoans) {
    // Only certain roles can set SUSPENDED or BLACKLISTED
    if ((newStatus === 'SUSPENDED' || newStatus === 'BLACKLISTED') && ![
        'SUPER_ADMIN',
        'HIGHER_MANAGEMENT',
        'MANAGER'
    ].includes(userRole)) {
        return {
            isValid: false,
            reason: 'Insufficient permissions to suspend or blacklist customers'
        };
    }
    // Cannot set to ACTIVE without active loans
    if (newStatus === 'ACTIVE' && !hasActiveLoans) {
        return {
            isValid: false,
            reason: 'Cannot set customer to ACTIVE status without active loans'
        };
    }
    // All other changes are valid
    return {
        isValid: true
    };
}
function getStatusBadgeColor(status) {
    switch(status){
        case 'ACTIVE':
            return 'bg-green-100 text-green-800';
        case 'INACTIVE':
            return 'bg-gray-100 text-gray-800';
        case 'SUSPENDED':
            return 'bg-yellow-100 text-yellow-800';
        case 'BLACKLISTED':
            return 'bg-red-100 text-red-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
}
function getStatusLabel(status) {
    switch(status){
        case 'ACTIVE':
            return 'Active';
        case 'INACTIVE':
            return 'Inactive';
        case 'SUSPENDED':
            return 'Suspended';
        case 'BLACKLISTED':
            return 'Blacklisted';
        default:
            return 'Unknown';
    }
}
}),
"[project]/src/lib/interest-calculations.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * Interest calculation utilities for different loan calculation methods
 */ __turbopack_context__.s({
    "calculateLoanInterest": ()=>calculateLoanInterest,
    "convertTenureToDays": ()=>convertTenureToDays,
    "getEMIFrequencyLabel": ()=>getEMIFrequencyLabel
});
/**
 * Method 1: Monthly Interest Calculation
 * Formula: Principal × (Monthly Rate/100) × (Tenure in Months)
 * Example: 100,000 × 5% × 2 months = 10,000 interest
 * Note: interestRate is treated as MONTHLY rate for this method
 */ function calculateMonthlyInterest(input) {
    const { principalAmount, interestRate, tenureInDays, collectionType } = input;
    // Convert tenure to months (30 days = 1 month)
    const tenureInMonths = tenureInDays / 30;
    // Calculate total interest: Principal × Monthly Rate × Months
    // interestRate is already monthly rate for this method
    const totalInterest = principalAmount * (interestRate / 100) * tenureInMonths;
    // Total amount to be repaid
    const totalAmount = principalAmount + totalInterest;
    // Calculate number of payments based on collection type
    let numberOfPayments;
    switch(collectionType){
        case 'DAILY':
            numberOfPayments = tenureInDays;
            break;
        case 'WEEKLY':
            numberOfPayments = Math.ceil(tenureInDays / 7);
            break;
        case 'MONTHLY':
            numberOfPayments = Math.ceil(tenureInMonths);
            break;
        case 'QUARTERLY':
            numberOfPayments = Math.ceil(tenureInMonths / 3);
            break;
        case 'YEARLY':
            numberOfPayments = Math.ceil(tenureInMonths / 12);
            break;
        default:
            numberOfPayments = tenureInDays;
    }
    // EMI = Total Amount / Number of Payments
    const emiAmount = totalAmount / numberOfPayments;
    return {
        totalInterest,
        totalAmount,
        emiAmount,
        numberOfPayments
    };
}
/**
 * Method 2: Compound Interest EMI Calculation
 * Formula: EMI = P × r × (1+r)^n / ((1+r)^n - 1)
 * This is the standard loan EMI calculation with compound interest
 * Note: interestRate is treated as ANNUAL rate for this method
 */ function calculateCompoundInterest(input) {
    const { principalAmount, interestRate, tenureInDays, collectionType } = input;
    // Determine periods based on collection type
    let periodsPerYear;
    let totalPeriods;
    switch(collectionType){
        case 'DAILY':
            periodsPerYear = 365;
            totalPeriods = tenureInDays;
            break;
        case 'WEEKLY':
            periodsPerYear = 52;
            totalPeriods = Math.ceil(tenureInDays / 7);
            break;
        case 'MONTHLY':
            periodsPerYear = 12;
            totalPeriods = Math.ceil(tenureInDays / 30);
            break;
        case 'QUARTERLY':
            periodsPerYear = 4;
            totalPeriods = Math.ceil(tenureInDays / 90);
            break;
        case 'YEARLY':
            periodsPerYear = 1;
            totalPeriods = Math.ceil(tenureInDays / 365);
            break;
        default:
            periodsPerYear = 12;
            totalPeriods = Math.ceil(tenureInDays / 30);
    }
    // Calculate period interest rate
    const periodRate = interestRate / 100 / periodsPerYear;
    // Calculate EMI using compound interest formula
    let emiAmount;
    if (periodRate === 0) {
        // If no interest, just divide principal by periods
        emiAmount = principalAmount / totalPeriods;
    } else {
        // Standard EMI formula: EMI = P × r × (1+r)^n / ((1+r)^n - 1)
        emiAmount = principalAmount * periodRate * Math.pow(1 + periodRate, totalPeriods) / (Math.pow(1 + periodRate, totalPeriods) - 1);
    }
    // Calculate total amount and interest
    const totalAmount = emiAmount * totalPeriods;
    const totalInterest = totalAmount - principalAmount;
    return {
        totalInterest,
        totalAmount,
        emiAmount,
        numberOfPayments: totalPeriods
    };
}
function calculateLoanInterest(input) {
    switch(input.interestCalculationMethod){
        case 'MONTHLY_INTEREST':
            return calculateMonthlyInterest(input);
        case 'COMPOUND_INTEREST':
            return calculateCompoundInterest(input);
        default:
            throw new Error(`Unsupported interest calculation method: ${input.interestCalculationMethod}`);
    }
}
function convertTenureToDays(tenure, unit) {
    switch(unit){
        case 'DAYS':
            return tenure;
        case 'WEEKS':
            return tenure * 7;
        case 'MONTHS':
            return tenure * 30;
        case 'YEARS':
            return tenure * 365;
        default:
            return tenure;
    }
}
function getEMIFrequencyLabel(collectionType) {
    switch(collectionType){
        case 'DAILY':
            return 'Daily Payment';
        case 'WEEKLY':
            return 'Weekly EMI';
        case 'MONTHLY':
            return 'Monthly EMI';
        case 'QUARTERLY':
            return 'Quarterly EMI';
        case 'YEARLY':
            return 'Yearly EMI';
        default:
            return 'Payment';
    }
}
}),
"[project]/src/lib/payment-schedule.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * Unified payment schedule generation that uses the correct interest calculation method
 */ __turbopack_context__.s({
    "generatePaymentSchedule": ()=>generatePaymentSchedule
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$interest$2d$calculations$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/interest-calculations.ts [app-route] (ecmascript)");
;
function generatePaymentSchedule(loan) {
    const principal = Number(loan.principalAmount);
    const interestRate = Number(loan.interestRate);
    const tenure = Number(loan.tenure);
    // Convert tenure to days
    const tenureInDays = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$interest$2d$calculations$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["convertTenureToDays"])(tenure, loan.tenureUnit);
    // Map repayment frequency to collection type
    const collectionType = mapRepaymentFrequencyToCollectionType(loan.repaymentFrequency);
    // Calculate loan details using the specified method
    const calculation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$interest$2d$calculations$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["calculateLoanInterest"])({
        principalAmount: principal,
        interestRate: interestRate,
        tenureInDays: tenureInDays,
        collectionType: collectionType,
        interestCalculationMethod: loan.interestCalculationMethod
    });
    // Generate schedule based on the calculation method
    if (loan.interestCalculationMethod === 'MONTHLY_INTEREST') {
        return generateMonthlyInterestSchedule(loan, calculation);
    } else {
        return generateCompoundInterestSchedule(loan, calculation);
    }
}
/**
 * Generate schedule for Monthly Interest method (Simple Interest)
 * Each payment has equal installment amount with varying principal/interest split
 */ function generateMonthlyInterestSchedule(loan, calculation) {
    const schedule = [];
    const startDate = new Date(loan.disbursementDate || loan.applicationDate || new Date());
    // For monthly interest, each payment is the same amount
    const installmentAmount = calculation.emiAmount;
    const totalInterest = calculation.totalInterest;
    const numberOfPayments = calculation.numberOfPayments;
    // Distribute interest evenly across payments (simple interest approach)
    const interestPerPayment = totalInterest / numberOfPayments;
    const principalPerPayment = Number(loan.principalAmount) / numberOfPayments;
    for(let i = 1; i <= numberOfPayments; i++){
        const dueDate = getNextDueDate(startDate, loan.repaymentFrequency, i);
        schedule.push({
            installmentNumber: i,
            dueDate,
            principalAmount: Math.round(principalPerPayment * 100) / 100,
            interestAmount: Math.round(interestPerPayment * 100) / 100,
            totalAmount: Math.round(installmentAmount * 100) / 100,
            paidAmount: 0,
            status: 'PENDING'
        });
    }
    return schedule;
}
/**
 * Generate schedule for Compound Interest method (EMI)
 * Principal and interest amounts vary each payment, but total EMI remains constant
 */ function generateCompoundInterestSchedule(loan, calculation) {
    const schedule = [];
    const principal = Number(loan.principalAmount);
    const annualRate = Number(loan.interestRate) / 100;
    const startDate = new Date(loan.disbursementDate || loan.applicationDate || new Date());
    // Determine period rate based on frequency
    let periodsPerYear;
    switch(loan.repaymentFrequency){
        case 'DAILY':
            periodsPerYear = 365;
            break;
        case 'WEEKLY':
            periodsPerYear = 52;
            break;
        case 'MONTHLY':
            periodsPerYear = 12;
            break;
        case 'QUARTERLY':
            periodsPerYear = 4;
            break;
        case 'YEARLY':
            periodsPerYear = 1;
            break;
        default:
            periodsPerYear = 12;
    }
    const periodRate = annualRate / periodsPerYear;
    const emiAmount = calculation.emiAmount;
    const numberOfPayments = calculation.numberOfPayments;
    let remainingPrincipal = principal;
    for(let i = 1; i <= numberOfPayments; i++){
        const dueDate = getNextDueDate(startDate, loan.repaymentFrequency, i);
        // Calculate interest for this period
        const interestAmount = remainingPrincipal * periodRate;
        // Calculate principal payment (EMI - Interest)
        const principalAmount = Math.min(emiAmount - interestAmount, remainingPrincipal);
        // Update remaining principal
        remainingPrincipal -= principalAmount;
        schedule.push({
            installmentNumber: i,
            dueDate,
            principalAmount: Math.round(principalAmount * 100) / 100,
            interestAmount: Math.round(interestAmount * 100) / 100,
            totalAmount: Math.round(emiAmount * 100) / 100,
            paidAmount: 0,
            status: 'PENDING'
        });
        // Break if principal is fully paid
        if (remainingPrincipal <= 0.01) break;
    }
    return schedule;
}
/**
 * Calculate next due date based on frequency
 */ function getNextDueDate(startDate, frequency, installmentNumber) {
    const date = new Date(startDate);
    switch(frequency){
        case 'DAILY':
            // For daily payments, add the installment number as calendar days
            // This ensures that a 90-day loan has exactly 90 daily payments
            date.setDate(date.getDate() + installmentNumber);
            return date;
        case 'WEEKLY':
            const weeklyDate = new Date(date);
            weeklyDate.setDate(weeklyDate.getDate() + installmentNumber * 7);
            return weeklyDate;
        case 'MONTHLY':
            const monthlyDate = new Date(date);
            monthlyDate.setMonth(monthlyDate.getMonth() + installmentNumber);
            return monthlyDate;
        case 'QUARTERLY':
            const quarterlyDate = new Date(date);
            quarterlyDate.setMonth(quarterlyDate.getMonth() + installmentNumber * 3);
            return quarterlyDate;
        case 'YEARLY':
            const yearlyDate = new Date(date);
            yearlyDate.setFullYear(yearlyDate.getFullYear() + installmentNumber);
            return yearlyDate;
        default:
            const defaultDate = new Date(date);
            defaultDate.setMonth(defaultDate.getMonth() + installmentNumber);
            return defaultDate;
    }
}
/**
 * Map repayment frequency to collection type for interest calculations
 */ function mapRepaymentFrequencyToCollectionType(frequency) {
    switch(frequency){
        case 'DAILY':
            return 'DAILY';
        case 'WEEKLY':
            return 'WEEKLY';
        case 'MONTHLY':
            return 'MONTHLY';
        case 'QUARTERLY':
            return 'QUARTERLY';
        case 'YEARLY':
            return 'YEARLY';
        default:
            return 'MONTHLY';
    }
}
}),
"[project]/src/app/api/loans/[id]/disburse/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": ()=>GET,
    "POST": ()=>POST
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$customer$2d$status$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/customer-status.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$payment$2d$schedule$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/payment-schedule.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-route] (ecmascript) <export * as z>");
;
;
;
;
;
;
;
const disbursementSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    disbursedAmount: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive('Disbursed amount must be positive'),
    disbursementMethod: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
        'CASH',
        'BANK_TRANSFER',
        'CHEQUE',
        'ONLINE',
        'MOBILE_PAYMENT'
    ]),
    disbursementReference: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    disbursementNotes: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    disbursementDate: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().transform((str)=>new Date(str)).optional()
});
async function POST(request, { params }) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!session || !await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["hasPermission"])(session.user.role, 'loans:disburse')) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized - Loan disbursement permission required'
            }, {
                status: 401
            });
        }
        const { id } = await params;
        const body = await request.json();
        const validatedData = disbursementSchema.parse(body);
        // Check if loan exists and is approved
        const loan = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].loan.findUnique({
            where: {
                id
            },
            include: {
                customer: {
                    select: {
                        firstName: true,
                        lastName: true,
                        email: true,
                        phone: true
                    }
                },
                loanType: {
                    select: {
                        interestCalculationMethod: true,
                        tenureUnit: true,
                        name: true
                    }
                }
            }
        });
        if (!loan) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Loan not found'
            }, {
                status: 404
            });
        }
        if (loan.status !== 'APPROVED') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: `Loan cannot be disbursed. Current status: ${loan.status}. Only approved loans can be disbursed.`
            }, {
                status: 400
            });
        }
        // Validate disbursed amount doesn't exceed principal amount
        if (validatedData.disbursedAmount > Number(loan.principalAmount)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: `Disbursed amount (${validatedData.disbursedAmount}) cannot exceed principal amount (${loan.principalAmount})`
            }, {
                status: 400
            });
        }
        // Update loan with disbursement details
        const updatedLoan = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$transaction(async (tx)=>{
            // Update loan status and disbursement details
            const loan = await tx.loan.update({
                where: {
                    id
                },
                data: {
                    status: 'ACTIVE',
                    disbursedAmount: validatedData.disbursedAmount,
                    disbursementMethod: validatedData.disbursementMethod,
                    disbursementReference: validatedData.disbursementReference || null,
                    disbursementNotes: validatedData.disbursementNotes || null,
                    disbursementDate: validatedData.disbursementDate || new Date(),
                    disbursedBy: session.user.id,
                    disbursedAt: new Date(),
                    updatedAt: new Date()
                },
                include: {
                    customer: {
                        select: {
                            firstName: true,
                            lastName: true,
                            phone: true,
                            email: true
                        }
                    },
                    loanType: {
                        select: {
                            name: true
                        }
                    }
                }
            });
            // Delete any existing payment schedules (in case they were created during approval)
            await tx.paymentSchedule.deleteMany({
                where: {
                    loanId: loan.id
                }
            });
            // Create payment schedule now that loan is disbursed
            const disbursementDate = validatedData.disbursementDate || new Date();
            const loanData = {
                id: loan.id,
                principalAmount: Number(loan.principalAmount),
                interestRate: Number(loan.interestRate),
                tenure: Number(loan.tenure),
                tenureUnit: loan.loanType?.tenureUnit || 'MONTHS',
                repaymentFrequency: loan.repaymentFrequency,
                interestCalculationMethod: loan.loanType?.interestCalculationMethod || 'MONTHLY_INTEREST',
                applicationDate: loan.applicationDate,
                disbursementDate: disbursementDate
            };
            console.log('Creating payment schedule for loan:', {
                loanId: loan.id,
                loanNumber: loan.loanNumber,
                loanData
            });
            const schedule = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$payment$2d$schedule$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["generatePaymentSchedule"])(loanData);
            console.log('Generated payment schedule:', {
                numberOfPayments: schedule.length,
                firstPayment: schedule[0],
                lastPayment: schedule[schedule.length - 1]
            });
            // Save the generated schedule to database
            const scheduleData = schedule.map((item)=>({
                    loanId: loan.id,
                    installmentNumber: item.installmentNumber,
                    dueDate: item.dueDate,
                    principalAmount: item.principalAmount,
                    interestAmount: item.interestAmount,
                    totalAmount: item.totalAmount,
                    status: 'PENDING'
                }));
            console.log('Saving payment schedule data:', scheduleData.slice(0, 2)); // Log first 2 items
            await tx.paymentSchedule.createMany({
                data: scheduleData
            });
            // Create audit log entry
            await tx.auditLog.create({
                data: {
                    action: 'DISBURSE',
                    resource: 'Loan',
                    resourceId: id,
                    userId: session.user.id,
                    newValues: {
                        status: 'ACTIVE',
                        loanNumber: loan.loanNumber,
                        customerName: `${loan.customer.firstName} ${loan.customer.lastName}`,
                        disbursedAmount: validatedData.disbursedAmount,
                        disbursementMethod: validatedData.disbursementMethod,
                        disbursementReference: validatedData.disbursementReference,
                        disbursementDate: disbursementDate.toISOString()
                    }
                }
            });
            return loan;
        });
        // Update customer status to ACTIVE since they now have a disbursed loan
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$customer$2d$status$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["updateCustomerStatus"])(loan.customerId, session.user.id).catch((error)=>{
            console.error('Error updating customer status after disbursement:', error);
        });
        // TODO: Send notification to customer (email/SMS)
        // TODO: Generate disbursement receipt/document
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            message: `Loan disbursed successfully`,
            loan: updatedLoan,
            disbursement: {
                amount: validatedData.disbursedAmount,
                method: validatedData.disbursementMethod,
                reference: validatedData.disbursementReference,
                date: validatedData.disbursementDate || new Date(),
                disbursedBy: {
                    id: session.user.id,
                    name: `${session.user.firstName} ${session.user.lastName}`,
                    role: session.user.role
                }
            }
        });
    } catch (error) {
        console.error('Loan disbursement error:', error);
        if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].ZodError) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Validation error',
                details: error.errors
            }, {
                status: 400
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to disburse loan'
        }, {
            status: 500
        });
    }
}
async function GET(request, { params }) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!session || !await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["hasPermission"])(session.user.role, 'loans:read')) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized'
            }, {
                status: 401
            });
        }
        const { id } = await params;
        const loan = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].loan.findUnique({
            where: {
                id
            },
            select: {
                id: true,
                loanNumber: true,
                status: true,
                principalAmount: true,
                disbursedAmount: true,
                disbursementMethod: true,
                disbursementReference: true,
                disbursementNotes: true,
                disbursementDate: true,
                disbursedAt: true,
                disburser: {
                    select: {
                        firstName: true,
                        lastName: true,
                        role: true
                    }
                }
            }
        });
        if (!loan) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Loan not found'
            }, {
                status: 404
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            loan
        });
    } catch (error) {
        console.error('Error fetching disbursement details:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to fetch disbursement details'
        }, {
            status: 500
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__9625fd0c._.js.map