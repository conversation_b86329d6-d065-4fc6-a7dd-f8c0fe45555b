{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Cred<PERSON><PERSON><PERSON>rovider from 'next-auth/providers/credentials'\nimport { PrismaAdapter } from '@auth/prisma-adapter'\nimport { prisma } from './prisma'\nimport bcrypt from 'bcryptjs'\nimport { UserRole } from '@prisma/client'\n\n// Cache for role permissions to avoid database calls on every request\nlet rolePermissionsCache: Record<string, string[]> = {}\nlet cacheLastUpdated = 0\nconst CACHE_DURATION = 5 * 60 * 1000 // 5 minutes\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma) as any,\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user || !user.isActive) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        // Update last login\n        await prisma.user.update({\n          where: { id: user.id },\n          data: { lastLogin: new Date() }\n        })\n\n        return {\n          id: user.id,\n          email: user.email,\n          firstName: user.firstName,\n          lastName: user.lastName,\n          role: user.role,\n          avatar: user.avatar || undefined,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n        token.firstName = user.firstName\n        token.lastName = user.lastName\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as UserRole\n        session.user.firstName = token.firstName as string\n        session.user.lastName = token.lastName as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n    error: '/auth/error',\n  }\n}\n\n// Load role permissions from database with caching\nasync function loadRolePermissions(): Promise<Record<string, string[]>> {\n  const now = Date.now()\n\n  // Return cached permissions if still valid\n  if (cacheLastUpdated && (now - cacheLastUpdated) < CACHE_DURATION) {\n    return rolePermissionsCache\n  }\n\n  try {\n    // Fetch all active role permissions from database\n    const permissions = await prisma.rolePermission.findMany({\n      where: { isActive: true },\n      select: { role: true, permission: true }\n    })\n\n    // Group permissions by role\n    const rolePermissions: Record<string, string[]> = {}\n    for (const perm of permissions) {\n      if (!rolePermissions[perm.role]) {\n        rolePermissions[perm.role] = []\n      }\n      rolePermissions[perm.role].push(perm.permission)\n    }\n\n    // Update cache\n    rolePermissionsCache = rolePermissions\n    cacheLastUpdated = now\n\n    return rolePermissions\n  } catch (error) {\n    console.error('Error loading role permissions from database:', error)\n    // Return empty permissions on error to be safe\n    return {}\n  }\n}\n\n// Get role permissions (cached)\nexport async function getRolePermissions(): Promise<Record<string, string[]>> {\n  return await loadRolePermissions()\n}\n\n// Clear permissions cache (call this when permissions are updated)\nexport function clearPermissionsCache() {\n  rolePermissionsCache = {}\n  cacheLastUpdated = 0\n}\n\n// Initialize permissions cache on server startup\nexport async function initializePermissionsCache() {\n  try {\n    await loadRolePermissions()\n    console.log('✅ Permissions cache initialized')\n  } catch (error) {\n    console.error('❌ Failed to initialize permissions cache:', error)\n  }\n}\n\nexport async function hasPermission(userRole: UserRole, permission: string): Promise<boolean> {\n  const rolePermissions = await getRolePermissions()\n  return rolePermissions[userRole]?.includes(permission) || false\n}\n\nexport async function checkPermissions(userRole: UserRole, requiredPermissions: string[]): Promise<boolean> {\n  const rolePermissions = await getRolePermissions()\n  return requiredPermissions.every(permission => rolePermissions[userRole]?.includes(permission) || false)\n}\n\n// Synchronous version for backward compatibility (uses cache)\nexport function hasPermissionSync(userRole: UserRole, permission: string): boolean {\n  return rolePermissionsCache[userRole]?.includes(permission) || false\n}\n\nexport function checkPermissionsSync(userRole: UserRole, requiredPermissions: string[]): boolean {\n  return requiredPermissions.every(permission => hasPermissionSync(userRole, permission))\n}\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AACA;AACA;AACA;;;;;AAGA,sEAAsE;AACtE,IAAI,uBAAiD,CAAC;AACtD,IAAI,mBAAmB;AACvB,MAAM,iBAAiB,IAAI,KAAK,KAAK,YAAY;;AAE1C,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;oBAC3B,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,oBAAoB;gBACpB,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;oBACvB,OAAO;wBAAE,IAAI,KAAK,EAAE;oBAAC;oBACrB,MAAM;wBAAE,WAAW,IAAI;oBAAO;gBAChC;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,WAAW,KAAK,SAAS;oBACzB,UAAU,KAAK,QAAQ;oBACvB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM,IAAI;gBACzB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;AACF;AAEA,mDAAmD;AACnD,eAAe;IACb,MAAM,MAAM,KAAK,GAAG;IAEpB,2CAA2C;IAC3C,IAAI,oBAAoB,AAAC,MAAM,mBAAoB,gBAAgB;QACjE,OAAO;IACT;IAEA,IAAI;QACF,kDAAkD;QAClD,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;YACvD,OAAO;gBAAE,UAAU;YAAK;YACxB,QAAQ;gBAAE,MAAM;gBAAM,YAAY;YAAK;QACzC;QAEA,4BAA4B;QAC5B,MAAM,kBAA4C,CAAC;QACnD,KAAK,MAAM,QAAQ,YAAa;YAC9B,IAAI,CAAC,eAAe,CAAC,KAAK,IAAI,CAAC,EAAE;gBAC/B,eAAe,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE;YACjC;YACA,eAAe,CAAC,KAAK,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,UAAU;QACjD;QAEA,eAAe;QACf,uBAAuB;QACvB,mBAAmB;QAEnB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iDAAiD;QAC/D,+CAA+C;QAC/C,OAAO,CAAC;IACV;AACF;AAGO,eAAe;IACpB,OAAO,MAAM;AACf;AAGO,SAAS;IACd,uBAAuB,CAAC;IACxB,mBAAmB;AACrB;AAGO,eAAe;IACpB,IAAI;QACF,MAAM;QACN,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;IAC7D;AACF;AAEO,eAAe,cAAc,QAAkB,EAAE,UAAkB;IACxE,MAAM,kBAAkB,MAAM;IAC9B,OAAO,eAAe,CAAC,SAAS,EAAE,SAAS,eAAe;AAC5D;AAEO,eAAe,iBAAiB,QAAkB,EAAE,mBAA6B;IACtF,MAAM,kBAAkB,MAAM;IAC9B,OAAO,oBAAoB,KAAK,CAAC,CAAA,aAAc,eAAe,CAAC,SAAS,EAAE,SAAS,eAAe;AACpG;AAGO,SAAS,kBAAkB,QAAkB,EAAE,UAAkB;IACtE,OAAO,oBAAoB,CAAC,SAAS,EAAE,SAAS,eAAe;AACjE;AAEO,SAAS,qBAAqB,QAAkB,EAAE,mBAA6B;IACpF,OAAO,oBAAoB,KAAK,CAAC,CAAA,aAAc,kBAAkB,UAAU;AAC7E", "debugId": null}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/app/api/reports/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions, hasPermission } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\n\n// Helper function to convert BigInt values to numbers recursively\nfunction convertBigIntToNumber(obj: any): any {\n  if (obj === null || obj === undefined) {\n    return obj\n  }\n\n  if (typeof obj === 'bigint') {\n    return Number(obj)\n  }\n\n  if (Array.isArray(obj)) {\n    return obj.map(convertBigIntToNumber)\n  }\n\n  if (typeof obj === 'object') {\n    const converted: any = {}\n    for (const [key, value] of Object.entries(obj)) {\n      converted[key] = convertBigIntToNumber(value)\n    }\n    return converted\n  }\n\n  return obj\n}\n\nexport async function GET(request: NextRequest) {\n  try {\n    const session = await getServerSession(authOptions)\n    \n    if (!session || !await hasPermission(session.user.role, 'reports:read')) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const { searchParams } = new URL(request.url)\n    const reportType = searchParams.get('type') || 'overview'\n    const startDate = searchParams.get('startDate')\n    const endDate = searchParams.get('endDate')\n\n    const start = startDate ? new Date(startDate) : new Date(new Date().getFullYear(), new Date().getMonth(), 1)\n    const end = endDate ? new Date(endDate) : new Date()\n\n    switch (reportType) {\n      case 'overview':\n        return await getOverviewReport(start, end)\n      case 'loans':\n        return await getLoansReport(start, end)\n      case 'payments':\n        return await getPaymentsReport(start, end)\n      case 'customers':\n        return await getCustomersReport(start, end)\n      case 'portfolio':\n        return await getPortfolioReport(start, end)\n      default:\n        return NextResponse.json({ error: 'Invalid report type' }, { status: 400 })\n    }\n  } catch (error) {\n    console.error('Error generating report:', error)\n    return NextResponse.json(\n      { error: 'Failed to generate report' },\n      { status: 500 }\n    )\n  }\n}\n\nasync function getOverviewReport(startDate: Date, endDate: Date) {\n  try {\n    console.log('Getting overview report for period:', startDate, 'to', endDate)\n\n    const [\n      totalLoans,\n      totalCustomers,\n      totalDisbursed,\n      totalCollected,\n      activeLoans,\n      overdueLoans,\n      recentActivities\n    ] = await Promise.all([\n    // Total loans\n    prisma.loan.count(),\n    \n    // Total customers\n    prisma.customer.count(),\n    \n    // Total disbursed amount (all time for overview, but within date range for specific period analysis)\n    prisma.loan.aggregate({\n      where: {\n        status: { in: ['DISBURSED', 'ACTIVE', 'COMPLETED'] },\n        disbursementDate: { not: null }\n      },\n      _sum: { disbursedAmount: true }\n    }),\n\n    // Total collected amount (within date range)\n    prisma.payment.aggregate({\n      where: {\n        paymentDate: { gte: startDate, lte: endDate }\n      },\n      _sum: { amount: true }\n    }),\n    \n    // Active loans\n    prisma.loan.count({\n      where: { status: { in: ['ACTIVE', 'DISBURSED'] } }\n    }),\n    \n    // Overdue loans (simplified - would need proper overdue logic)\n    prisma.loan.count({\n      where: { \n        status: 'ACTIVE',\n        // Add overdue logic based on payment schedules\n      }\n    }),\n    \n    // Recent activities from audit logs\n    prisma.auditLog.findMany({\n      take: 10,\n      orderBy: { timestamp: 'desc' },\n      where: {\n        timestamp: { gte: startDate, lte: endDate }\n      },\n      include: {\n        user: {\n          select: {\n            firstName: true,\n            lastName: true\n          }\n        }\n      }\n    })\n    ])\n\n    console.log('Overview report data:', {\n      totalLoans,\n      totalCustomers,\n      totalDisbursed: totalDisbursed._sum.disbursedAmount,\n      totalCollected: totalCollected._sum.amount,\n      activeLoans,\n      overdueLoans,\n      recentActivitiesCount: recentActivities.length\n    })\n\n    const result = {\n      overview: {\n        totalLoans,\n        totalCustomers,\n        totalDisbursed: totalDisbursed._sum.disbursedAmount || 0,\n        totalCollected: totalCollected._sum.amount || 0,\n        activeLoans,\n        overdueLoans\n      },\n      recentActivities,\n      period: { startDate, endDate }\n    }\n\n    return NextResponse.json(result)\n  } catch (error) {\n    console.error('Error in getOverviewReport:', error)\n    return NextResponse.json({\n      overview: {\n        totalLoans: 0,\n        totalCustomers: 0,\n        totalDisbursed: 0,\n        totalCollected: 0,\n        activeLoans: 0,\n        overdueLoans: 0\n      },\n      recentActivities: [],\n      period: { startDate, endDate },\n      error: 'Failed to generate overview report'\n    })\n  }\n}\n\nasync function getLoansReport(startDate: Date, endDate: Date) {\n  try {\n    console.log('Getting loans report for period:', startDate, 'to', endDate)\n\n    const [\n      loansByStatus,\n      loansByType,\n      disbursementTrend,\n      averageLoanAmount\n    ] = await Promise.all([\n    // Loans by status\n    prisma.loan.groupBy({\n      by: ['status'],\n      _count: { id: true },\n      _sum: { principalAmount: true }\n    }),\n    \n    // Loans by type\n    prisma.loan.groupBy({\n      by: ['loanTypeId'],\n      _count: { id: true },\n      _sum: { principalAmount: true }\n    }),\n    \n    // Disbursement trend (monthly) - show all disbursements, not just within date range\n    prisma.$queryRaw`\n      SELECT\n        DATE_TRUNC('month', \"disbursementDate\") as month,\n        COUNT(*) as count,\n        SUM(\"disbursedAmount\") as amount\n      FROM loans\n      WHERE \"disbursementDate\" IS NOT NULL\n        AND \"disbursedAmount\" IS NOT NULL\n      GROUP BY DATE_TRUNC('month', \"disbursementDate\")\n      ORDER BY month DESC\n      LIMIT 12\n    `,\n\n    // Average loan amount (all loans)\n    prisma.loan.aggregate({\n      where: {\n        principalAmount: { gt: 0 }\n      },\n      _avg: { principalAmount: true }\n    })\n    ])\n\n    console.log('Loans report data:', {\n      loansByStatusCount: loansByStatus.length,\n      loansByTypeCount: loansByType.length,\n      disbursementTrendCount: disbursementTrend.length,\n      averageLoanAmount: averageLoanAmount._avg.principalAmount\n    })\n\n    // Convert all BigInt values to numbers for JSON serialization\n    const result = convertBigIntToNumber({\n      loansByStatus,\n      loansByType,\n      disbursementTrend,\n      averageLoanAmount: averageLoanAmount._avg.principalAmount || 0,\n      period: { startDate, endDate }\n    })\n\n    return NextResponse.json(result)\n  } catch (error) {\n    console.error('Error in getLoansReport:', error)\n    return NextResponse.json({\n      loansByStatus: [],\n      loansByType: [],\n      disbursementTrend: [],\n      averageLoanAmount: 0,\n      period: { startDate, endDate },\n      error: 'Failed to generate loans report'\n    })\n  }\n}\n\nasync function getPaymentsReport(startDate: Date, endDate: Date) {\n  try {\n    console.log('Getting payments report for period:', startDate, 'to', endDate)\n\n    const [\n      paymentsByMethod,\n      paymentTrend,\n      totalCollections,\n      averagePayment\n    ] = await Promise.all([\n    // Payments by method (all payments)\n    prisma.payment.groupBy({\n      by: ['paymentMethod'],\n      _count: { id: true },\n      _sum: { amount: true }\n    }),\n\n    // Payment trend (daily) - last 30 days or within date range\n    prisma.$queryRaw`\n      SELECT\n        DATE_TRUNC('day', \"paymentDate\") as day,\n        COUNT(*) as count,\n        SUM(\"amount\") as amount\n      FROM payments\n      WHERE \"paymentDate\" >= ${startDate}\n        AND \"paymentDate\" <= ${endDate}\n      GROUP BY DATE_TRUNC('day', \"paymentDate\")\n      ORDER BY day DESC\n    `,\n\n    // Total collections (within date range)\n    prisma.payment.aggregate({\n      where: {\n        paymentDate: { gte: startDate, lte: endDate }\n      },\n      _sum: { amount: true },\n      _count: { id: true }\n    }),\n\n    // Average payment (all payments)\n    prisma.payment.aggregate({\n      _avg: { amount: true }\n    })\n    ])\n\n    console.log('Payments report data:', {\n      paymentsByMethodCount: paymentsByMethod.length,\n      paymentTrendCount: paymentTrend.length,\n      totalCollections: totalCollections._sum.amount,\n      averagePayment: averagePayment._avg.amount\n    })\n\n    // Convert all BigInt values to numbers for JSON serialization\n    const result = convertBigIntToNumber({\n      paymentsByMethod,\n      paymentTrend,\n      totalCollections: {\n        amount: totalCollections._sum.amount || 0,\n        count: totalCollections._count.id || 0\n      },\n      averagePayment: averagePayment._avg.amount || 0,\n      period: { startDate, endDate }\n    })\n\n    return NextResponse.json(result)\n  } catch (error) {\n    console.error('Error in getPaymentsReport:', error)\n    return NextResponse.json({\n      paymentsByMethod: [],\n      paymentTrend: [],\n      totalCollections: { amount: 0, count: 0 },\n      averagePayment: 0,\n      period: { startDate, endDate },\n      error: 'Failed to generate payments report'\n    })\n  }\n}\n\nasync function getCustomersReport(startDate: Date, endDate: Date) {\n  try {\n    console.log('Getting customers report for period:', startDate, 'to', endDate)\n\n    const [\n      newCustomers,\n      customersByEmployment,\n      customersByCity,\n      averageIncome\n    ] = await Promise.all([\n    // New customers\n    prisma.customer.count({\n      where: {\n        createdAt: { gte: startDate, lte: endDate }\n      }\n    }),\n    \n    // Customers by employment type\n    prisma.customer.groupBy({\n      by: ['employmentType'],\n      _count: { id: true }\n    }),\n    \n    // Customers by city\n    prisma.customer.groupBy({\n      by: ['city'],\n      _count: { id: true }\n    }),\n    \n    // Average monthly income\n    prisma.customer.aggregate({\n      _avg: { monthlyIncome: true }\n    })\n    ])\n\n    console.log('Customers report data:', {\n      newCustomers,\n      customersByEmploymentCount: customersByEmployment.length,\n      customersByCityCount: customersByCity.length,\n      averageIncome: averageIncome._avg.monthlyIncome\n    })\n\n    const result = {\n      newCustomers,\n      customersByEmployment,\n      customersByCity,\n      averageIncome: averageIncome._avg.monthlyIncome || 0,\n      period: { startDate, endDate }\n    }\n\n    return NextResponse.json(result)\n  } catch (error) {\n    console.error('Error in getCustomersReport:', error)\n    return NextResponse.json({\n      newCustomers: 0,\n      customersByEmployment: [],\n      customersByCity: [],\n      averageIncome: 0,\n      period: { startDate, endDate },\n      error: 'Failed to generate customers report'\n    })\n  }\n}\n\nasync function getPortfolioReport(startDate: Date, endDate: Date) {\n  try {\n    console.log('Getting portfolio report for period:', startDate, 'to', endDate)\n\n    const [\n      portfolioValue,\n      outstandingAmount,\n      collectionRate,\n      riskAnalysis\n    ] = await Promise.all([\n    // Total portfolio value\n    prisma.loan.aggregate({\n      where: {\n        status: { in: ['ACTIVE', 'DISBURSED'] }\n      },\n      _sum: { totalAmount: true }\n    }),\n    \n    // Outstanding amount\n    prisma.loan.aggregate({\n      where: {\n        status: { in: ['ACTIVE', 'DISBURSED'] }\n      },\n      _sum: { disbursedAmount: true }\n    }),\n    \n    // Collection rate (simplified)\n    prisma.payment.aggregate({\n      where: {\n        paymentDate: { gte: startDate, lte: endDate }\n      },\n      _sum: { amount: true }\n    }),\n    \n    // Risk analysis (loans by status)\n    prisma.loan.groupBy({\n      by: ['status'],\n      _count: { id: true },\n      _sum: { disbursedAmount: true }\n    })\n    ])\n\n    console.log('Portfolio report data:', {\n      portfolioValue: portfolioValue._sum.totalAmount,\n      outstandingAmount: outstandingAmount._sum.disbursedAmount,\n      collectionRate: collectionRate._sum.amount,\n      riskAnalysisCount: riskAnalysis.length\n    })\n\n    const result = {\n      portfolioValue: portfolioValue._sum.totalAmount || 0,\n      outstandingAmount: outstandingAmount._sum.disbursedAmount || 0,\n      collectionRate: collectionRate._sum.amount || 0,\n      riskAnalysis,\n      period: { startDate, endDate }\n    }\n\n    return NextResponse.json(result)\n  } catch (error) {\n    console.error('Error in getPortfolioReport:', error)\n    return NextResponse.json({\n      portfolioValue: 0,\n      outstandingAmount: 0,\n      collectionRate: 0,\n      riskAnalysis: [],\n      period: { startDate, endDate },\n      error: 'Failed to generate portfolio report'\n    })\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEA,kEAAkE;AAClE,SAAS,sBAAsB,GAAQ;IACrC,IAAI,QAAQ,QAAQ,QAAQ,WAAW;QACrC,OAAO;IACT;IAEA,IAAI,OAAO,QAAQ,UAAU;QAC3B,OAAO,OAAO;IAChB;IAEA,IAAI,MAAM,OAAO,CAAC,MAAM;QACtB,OAAO,IAAI,GAAG,CAAC;IACjB;IAEA,IAAI,OAAO,QAAQ,UAAU;QAC3B,MAAM,YAAiB,CAAC;QACxB,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,KAAM;YAC9C,SAAS,CAAC,IAAI,GAAG,sBAAsB;QACzC;QACA,OAAO;IACT;IAEA,OAAO;AACT;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,WAAW,CAAC,MAAM,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,IAAI,CAAC,IAAI,EAAE,iBAAiB;YACvE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,aAAa,aAAa,GAAG,CAAC,WAAW;QAC/C,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,UAAU,aAAa,GAAG,CAAC;QAEjC,MAAM,QAAQ,YAAY,IAAI,KAAK,aAAa,IAAI,KAAK,IAAI,OAAO,WAAW,IAAI,IAAI,OAAO,QAAQ,IAAI;QAC1G,MAAM,MAAM,UAAU,IAAI,KAAK,WAAW,IAAI;QAE9C,OAAQ;YACN,KAAK;gBACH,OAAO,MAAM,kBAAkB,OAAO;YACxC,KAAK;gBACH,OAAO,MAAM,eAAe,OAAO;YACrC,KAAK;gBACH,OAAO,MAAM,kBAAkB,OAAO;YACxC,KAAK;gBACH,OAAO,MAAM,mBAAmB,OAAO;YACzC,KAAK;gBACH,OAAO,MAAM,mBAAmB,OAAO;YACzC;gBACE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBAAE,OAAO;gBAAsB,GAAG;oBAAE,QAAQ;gBAAI;QAC7E;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,eAAe,kBAAkB,SAAe,EAAE,OAAa;IAC7D,IAAI;QACF,QAAQ,GAAG,CAAC,uCAAuC,WAAW,MAAM;QAEpE,MAAM,CACJ,YACA,gBACA,gBACA,gBACA,aACA,cACA,iBACD,GAAG,MAAM,QAAQ,GAAG,CAAC;YACtB,cAAc;YACd,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK;YAEjB,kBAAkB;YAClB,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,KAAK;YAErB,qGAAqG;YACrG,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;gBACpB,OAAO;oBACL,QAAQ;wBAAE,IAAI;4BAAC;4BAAa;4BAAU;yBAAY;oBAAC;oBACnD,kBAAkB;wBAAE,KAAK;oBAAK;gBAChC;gBACA,MAAM;oBAAE,iBAAiB;gBAAK;YAChC;YAEA,6CAA6C;YAC7C,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,SAAS,CAAC;gBACvB,OAAO;oBACL,aAAa;wBAAE,KAAK;wBAAW,KAAK;oBAAQ;gBAC9C;gBACA,MAAM;oBAAE,QAAQ;gBAAK;YACvB;YAEA,eAAe;YACf,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,OAAO;oBAAE,QAAQ;wBAAE,IAAI;4BAAC;4BAAU;yBAAY;oBAAC;gBAAE;YACnD;YAEA,+DAA+D;YAC/D,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;gBAChB,OAAO;oBACL,QAAQ;gBAEV;YACF;YAEA,oCAAoC;YACpC,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBACvB,MAAM;gBACN,SAAS;oBAAE,WAAW;gBAAO;gBAC7B,OAAO;oBACL,WAAW;wBAAE,KAAK;wBAAW,KAAK;oBAAQ;gBAC5C;gBACA,SAAS;oBACP,MAAM;wBACJ,QAAQ;4BACN,WAAW;4BACX,UAAU;wBACZ;oBACF;gBACF;YACF;SACC;QAED,QAAQ,GAAG,CAAC,yBAAyB;YACnC;YACA;YACA,gBAAgB,eAAe,IAAI,CAAC,eAAe;YACnD,gBAAgB,eAAe,IAAI,CAAC,MAAM;YAC1C;YACA;YACA,uBAAuB,iBAAiB,MAAM;QAChD;QAEA,MAAM,SAAS;YACb,UAAU;gBACR;gBACA;gBACA,gBAAgB,eAAe,IAAI,CAAC,eAAe,IAAI;gBACvD,gBAAgB,eAAe,IAAI,CAAC,MAAM,IAAI;gBAC9C;gBACA;YACF;YACA;YACA,QAAQ;gBAAE;gBAAW;YAAQ;QAC/B;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,UAAU;gBACR,YAAY;gBACZ,gBAAgB;gBAChB,gBAAgB;gBAChB,gBAAgB;gBAChB,aAAa;gBACb,cAAc;YAChB;YACA,kBAAkB,EAAE;YACpB,QAAQ;gBAAE;gBAAW;YAAQ;YAC7B,OAAO;QACT;IACF;AACF;AAEA,eAAe,eAAe,SAAe,EAAE,OAAa;IAC1D,IAAI;QACF,QAAQ,GAAG,CAAC,oCAAoC,WAAW,MAAM;QAEjE,MAAM,CACJ,eACA,aACA,mBACA,kBACD,GAAG,MAAM,QAAQ,GAAG,CAAC;YACtB,kBAAkB;YAClB,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,OAAO,CAAC;gBAClB,IAAI;oBAAC;iBAAS;gBACd,QAAQ;oBAAE,IAAI;gBAAK;gBACnB,MAAM;oBAAE,iBAAiB;gBAAK;YAChC;YAEA,gBAAgB;YAChB,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,OAAO,CAAC;gBAClB,IAAI;oBAAC;iBAAa;gBAClB,QAAQ;oBAAE,IAAI;gBAAK;gBACnB,MAAM;oBAAE,iBAAiB;gBAAK;YAChC;YAEA,oFAAoF;YACpF,sHAAA,CAAA,SAAM,CAAC,SAAS,CAAC;;;;;;;;;;;IAWjB,CAAC;YAED,kCAAkC;YAClC,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;gBACpB,OAAO;oBACL,iBAAiB;wBAAE,IAAI;oBAAE;gBAC3B;gBACA,MAAM;oBAAE,iBAAiB;gBAAK;YAChC;SACC;QAED,QAAQ,GAAG,CAAC,sBAAsB;YAChC,oBAAoB,cAAc,MAAM;YACxC,kBAAkB,YAAY,MAAM;YACpC,wBAAwB,kBAAkB,MAAM;YAChD,mBAAmB,kBAAkB,IAAI,CAAC,eAAe;QAC3D;QAEA,8DAA8D;QAC9D,MAAM,SAAS,sBAAsB;YACnC;YACA;YACA;YACA,mBAAmB,kBAAkB,IAAI,CAAC,eAAe,IAAI;YAC7D,QAAQ;gBAAE;gBAAW;YAAQ;QAC/B;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,eAAe,EAAE;YACjB,aAAa,EAAE;YACf,mBAAmB,EAAE;YACrB,mBAAmB;YACnB,QAAQ;gBAAE;gBAAW;YAAQ;YAC7B,OAAO;QACT;IACF;AACF;AAEA,eAAe,kBAAkB,SAAe,EAAE,OAAa;IAC7D,IAAI;QACF,QAAQ,GAAG,CAAC,uCAAuC,WAAW,MAAM;QAEpE,MAAM,CACJ,kBACA,cACA,kBACA,eACD,GAAG,MAAM,QAAQ,GAAG,CAAC;YACtB,oCAAoC;YACpC,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,OAAO,CAAC;gBACrB,IAAI;oBAAC;iBAAgB;gBACrB,QAAQ;oBAAE,IAAI;gBAAK;gBACnB,MAAM;oBAAE,QAAQ;gBAAK;YACvB;YAEA,4DAA4D;YAC5D,sHAAA,CAAA,SAAM,CAAC,SAAS,CAAC;;;;;;6BAMQ,EAAE,UAAU;6BACZ,EAAE,QAAQ;;;IAGnC,CAAC;YAED,wCAAwC;YACxC,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,SAAS,CAAC;gBACvB,OAAO;oBACL,aAAa;wBAAE,KAAK;wBAAW,KAAK;oBAAQ;gBAC9C;gBACA,MAAM;oBAAE,QAAQ;gBAAK;gBACrB,QAAQ;oBAAE,IAAI;gBAAK;YACrB;YAEA,iCAAiC;YACjC,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,SAAS,CAAC;gBACvB,MAAM;oBAAE,QAAQ;gBAAK;YACvB;SACC;QAED,QAAQ,GAAG,CAAC,yBAAyB;YACnC,uBAAuB,iBAAiB,MAAM;YAC9C,mBAAmB,aAAa,MAAM;YACtC,kBAAkB,iBAAiB,IAAI,CAAC,MAAM;YAC9C,gBAAgB,eAAe,IAAI,CAAC,MAAM;QAC5C;QAEA,8DAA8D;QAC9D,MAAM,SAAS,sBAAsB;YACnC;YACA;YACA,kBAAkB;gBAChB,QAAQ,iBAAiB,IAAI,CAAC,MAAM,IAAI;gBACxC,OAAO,iBAAiB,MAAM,CAAC,EAAE,IAAI;YACvC;YACA,gBAAgB,eAAe,IAAI,CAAC,MAAM,IAAI;YAC9C,QAAQ;gBAAE;gBAAW;YAAQ;QAC/B;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,kBAAkB,EAAE;YACpB,cAAc,EAAE;YAChB,kBAAkB;gBAAE,QAAQ;gBAAG,OAAO;YAAE;YACxC,gBAAgB;YAChB,QAAQ;gBAAE;gBAAW;YAAQ;YAC7B,OAAO;QACT;IACF;AACF;AAEA,eAAe,mBAAmB,SAAe,EAAE,OAAa;IAC9D,IAAI;QACF,QAAQ,GAAG,CAAC,wCAAwC,WAAW,MAAM;QAErE,MAAM,CACJ,cACA,uBACA,iBACA,cACD,GAAG,MAAM,QAAQ,GAAG,CAAC;YACtB,gBAAgB;YAChB,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;gBACpB,OAAO;oBACL,WAAW;wBAAE,KAAK;wBAAW,KAAK;oBAAQ;gBAC5C;YACF;YAEA,+BAA+B;YAC/B,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,OAAO,CAAC;gBACtB,IAAI;oBAAC;iBAAiB;gBACtB,QAAQ;oBAAE,IAAI;gBAAK;YACrB;YAEA,oBAAoB;YACpB,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,OAAO,CAAC;gBACtB,IAAI;oBAAC;iBAAO;gBACZ,QAAQ;oBAAE,IAAI;gBAAK;YACrB;YAEA,yBAAyB;YACzB,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;gBACxB,MAAM;oBAAE,eAAe;gBAAK;YAC9B;SACC;QAED,QAAQ,GAAG,CAAC,0BAA0B;YACpC;YACA,4BAA4B,sBAAsB,MAAM;YACxD,sBAAsB,gBAAgB,MAAM;YAC5C,eAAe,cAAc,IAAI,CAAC,aAAa;QACjD;QAEA,MAAM,SAAS;YACb;YACA;YACA;YACA,eAAe,cAAc,IAAI,CAAC,aAAa,IAAI;YACnD,QAAQ;gBAAE;gBAAW;YAAQ;QAC/B;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,cAAc;YACd,uBAAuB,EAAE;YACzB,iBAAiB,EAAE;YACnB,eAAe;YACf,QAAQ;gBAAE;gBAAW;YAAQ;YAC7B,OAAO;QACT;IACF;AACF;AAEA,eAAe,mBAAmB,SAAe,EAAE,OAAa;IAC9D,IAAI;QACF,QAAQ,GAAG,CAAC,wCAAwC,WAAW,MAAM;QAErE,MAAM,CACJ,gBACA,mBACA,gBACA,aACD,GAAG,MAAM,QAAQ,GAAG,CAAC;YACtB,wBAAwB;YACxB,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;gBACpB,OAAO;oBACL,QAAQ;wBAAE,IAAI;4BAAC;4BAAU;yBAAY;oBAAC;gBACxC;gBACA,MAAM;oBAAE,aAAa;gBAAK;YAC5B;YAEA,qBAAqB;YACrB,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,SAAS,CAAC;gBACpB,OAAO;oBACL,QAAQ;wBAAE,IAAI;4BAAC;4BAAU;yBAAY;oBAAC;gBACxC;gBACA,MAAM;oBAAE,iBAAiB;gBAAK;YAChC;YAEA,+BAA+B;YAC/B,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,SAAS,CAAC;gBACvB,OAAO;oBACL,aAAa;wBAAE,KAAK;wBAAW,KAAK;oBAAQ;gBAC9C;gBACA,MAAM;oBAAE,QAAQ;gBAAK;YACvB;YAEA,kCAAkC;YAClC,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,OAAO,CAAC;gBAClB,IAAI;oBAAC;iBAAS;gBACd,QAAQ;oBAAE,IAAI;gBAAK;gBACnB,MAAM;oBAAE,iBAAiB;gBAAK;YAChC;SACC;QAED,QAAQ,GAAG,CAAC,0BAA0B;YACpC,gBAAgB,eAAe,IAAI,CAAC,WAAW;YAC/C,mBAAmB,kBAAkB,IAAI,CAAC,eAAe;YACzD,gBAAgB,eAAe,IAAI,CAAC,MAAM;YAC1C,mBAAmB,aAAa,MAAM;QACxC;QAEA,MAAM,SAAS;YACb,gBAAgB,eAAe,IAAI,CAAC,WAAW,IAAI;YACnD,mBAAmB,kBAAkB,IAAI,CAAC,eAAe,IAAI;YAC7D,gBAAgB,eAAe,IAAI,CAAC,MAAM,IAAI;YAC9C;YACA,QAAQ;gBAAE;gBAAW;YAAQ;QAC/B;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,gBAAgB;YAChB,mBAAmB;YACnB,gBAAgB;YAChB,cAAc,EAAE;YAChB,QAAQ;gBAAE;gBAAW;YAAQ;YAC7B,OAAO;QACT;IACF;AACF", "debugId": null}}]}