module.exports = {

"[project]/.next-internal/server/app/api/reports/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "prisma": ()=>prisma
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
const prisma = globalForPrisma.prisma ?? new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]();
if ("TURBOPACK compile-time truthy", 1) globalForPrisma.prisma = prisma;
}),
"[project]/src/lib/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "authOptions": ()=>authOptions,
    "checkPermissions": ()=>checkPermissions,
    "checkPermissionsSync": ()=>checkPermissionsSync,
    "clearPermissionsCache": ()=>clearPermissionsCache,
    "getRolePermissions": ()=>getRolePermissions,
    "hasPermission": ()=>hasPermission,
    "hasPermissionSync": ()=>hasPermissionSync,
    "initializePermissionsCache": ()=>initializePermissionsCache
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/credentials.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$auth$2f$prisma$2d$adapter$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@auth/prisma-adapter/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/bcryptjs/index.js [app-route] (ecmascript)");
;
;
;
;
// Cache for role permissions to avoid database calls on every request
let rolePermissionsCache = {};
let cacheLastUpdated = 0;
const CACHE_DURATION = 5 * 60 * 1000 // 5 minutes
;
const authOptions = {
    adapter: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$auth$2f$prisma$2d$adapter$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["PrismaAdapter"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"]),
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$credentials$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            name: 'credentials',
            credentials: {
                email: {
                    label: 'Email',
                    type: 'email'
                },
                password: {
                    label: 'Password',
                    type: 'password'
                }
            },
            async authorize (credentials) {
                if (!credentials?.email || !credentials?.password) {
                    return null;
                }
                const user = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.findUnique({
                    where: {
                        email: credentials.email
                    }
                });
                if (!user || !user.isActive) {
                    return null;
                }
                const isPasswordValid = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$bcryptjs$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].compare(credentials.password, user.password);
                if (!isPasswordValid) {
                    return null;
                }
                // Update last login
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].user.update({
                    where: {
                        id: user.id
                    },
                    data: {
                        lastLogin: new Date()
                    }
                });
                return {
                    id: user.id,
                    email: user.email,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    role: user.role,
                    avatar: user.avatar || undefined
                };
            }
        })
    ],
    session: {
        strategy: 'jwt'
    },
    callbacks: {
        async jwt ({ token, user }) {
            if (user) {
                token.role = user.role;
                token.firstName = user.firstName;
                token.lastName = user.lastName;
            }
            return token;
        },
        async session ({ session, token }) {
            if (token) {
                session.user.id = token.sub;
                session.user.role = token.role;
                session.user.firstName = token.firstName;
                session.user.lastName = token.lastName;
            }
            return session;
        }
    },
    pages: {
        signIn: '/auth/signin',
        error: '/auth/error'
    }
};
// Load role permissions from database with caching
async function loadRolePermissions() {
    const now = Date.now();
    // Return cached permissions if still valid
    if (cacheLastUpdated && now - cacheLastUpdated < CACHE_DURATION) {
        return rolePermissionsCache;
    }
    try {
        // Fetch all active role permissions from database
        const permissions = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].rolePermission.findMany({
            where: {
                isActive: true
            },
            select: {
                role: true,
                permission: true
            }
        });
        // Group permissions by role
        const rolePermissions = {};
        for (const perm of permissions){
            if (!rolePermissions[perm.role]) {
                rolePermissions[perm.role] = [];
            }
            rolePermissions[perm.role].push(perm.permission);
        }
        // Update cache
        rolePermissionsCache = rolePermissions;
        cacheLastUpdated = now;
        return rolePermissions;
    } catch (error) {
        console.error('Error loading role permissions from database:', error);
        // Return empty permissions on error to be safe
        return {};
    }
}
async function getRolePermissions() {
    return await loadRolePermissions();
}
function clearPermissionsCache() {
    rolePermissionsCache = {};
    cacheLastUpdated = 0;
}
async function initializePermissionsCache() {
    try {
        await loadRolePermissions();
        console.log('✅ Permissions cache initialized');
    } catch (error) {
        console.error('❌ Failed to initialize permissions cache:', error);
    }
}
async function hasPermission(userRole, permission) {
    const rolePermissions = await getRolePermissions();
    return rolePermissions[userRole]?.includes(permission) || false;
}
async function checkPermissions(userRole, requiredPermissions) {
    const rolePermissions = await getRolePermissions();
    return requiredPermissions.every((permission)=>rolePermissions[userRole]?.includes(permission) || false);
}
function hasPermissionSync(userRole, permission) {
    return rolePermissionsCache[userRole]?.includes(permission) || false;
}
function checkPermissionsSync(userRole, requiredPermissions) {
    return requiredPermissions.every((permission)=>hasPermissionSync(userRole, permission));
}
}),
"[project]/src/app/api/reports/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GET": ()=>GET
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
;
;
;
;
// Helper function to convert BigInt values to numbers recursively
function convertBigIntToNumber(obj) {
    if (obj === null || obj === undefined) {
        return obj;
    }
    if (typeof obj === 'bigint') {
        return Number(obj);
    }
    if (Array.isArray(obj)) {
        return obj.map(convertBigIntToNumber);
    }
    if (typeof obj === 'object') {
        const converted = {};
        for (const [key, value] of Object.entries(obj)){
            converted[key] = convertBigIntToNumber(value);
        }
        return converted;
    }
    return obj;
}
async function GET(request) {
    try {
        const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
        if (!session || !await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["hasPermission"])(session.user.role, 'reports:read')) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Unauthorized'
            }, {
                status: 401
            });
        }
        const { searchParams } = new URL(request.url);
        const reportType = searchParams.get('type') || 'overview';
        const startDate = searchParams.get('startDate');
        const endDate = searchParams.get('endDate');
        const start = startDate ? new Date(startDate) : new Date(new Date().getFullYear(), new Date().getMonth(), 1);
        const end = endDate ? new Date(endDate) : new Date();
        switch(reportType){
            case 'overview':
                return await getOverviewReport(start, end);
            case 'loans':
                return await getLoansReport(start, end);
            case 'payments':
                return await getPaymentsReport(start, end);
            case 'customers':
                return await getCustomersReport(start, end);
            case 'portfolio':
                return await getPortfolioReport(start, end);
            default:
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Invalid report type'
                }, {
                    status: 400
                });
        }
    } catch (error) {
        console.error('Error generating report:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to generate report'
        }, {
            status: 500
        });
    }
}
async function getOverviewReport(startDate, endDate) {
    try {
        console.log('Getting overview report for period:', startDate, 'to', endDate);
        const [totalLoans, totalCustomers, totalDisbursed, totalCollected, activeLoans, overdueLoans, recentActivities] = await Promise.all([
            // Total loans
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].loan.count(),
            // Total customers
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].customer.count(),
            // Total disbursed amount (all time for overview, but within date range for specific period analysis)
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].loan.aggregate({
                where: {
                    status: {
                        in: [
                            'DISBURSED',
                            'ACTIVE',
                            'COMPLETED'
                        ]
                    },
                    disbursementDate: {
                        not: null
                    }
                },
                _sum: {
                    disbursedAmount: true
                }
            }),
            // Total collected amount (within date range)
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].payment.aggregate({
                where: {
                    paymentDate: {
                        gte: startDate,
                        lte: endDate
                    }
                },
                _sum: {
                    amount: true
                }
            }),
            // Active loans
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].loan.count({
                where: {
                    status: {
                        in: [
                            'ACTIVE',
                            'DISBURSED'
                        ]
                    }
                }
            }),
            // Overdue loans (simplified - would need proper overdue logic)
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].loan.count({
                where: {
                    status: 'ACTIVE'
                }
            }),
            // Recent activities from audit logs
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].auditLog.findMany({
                take: 10,
                orderBy: {
                    timestamp: 'desc'
                },
                where: {
                    timestamp: {
                        gte: startDate,
                        lte: endDate
                    }
                },
                include: {
                    user: {
                        select: {
                            firstName: true,
                            lastName: true
                        }
                    }
                }
            })
        ]);
        console.log('Overview report data:', {
            totalLoans,
            totalCustomers,
            totalDisbursed: totalDisbursed._sum.disbursedAmount,
            totalCollected: totalCollected._sum.amount,
            activeLoans,
            overdueLoans,
            recentActivitiesCount: recentActivities.length
        });
        const result = {
            overview: {
                totalLoans,
                totalCustomers,
                totalDisbursed: totalDisbursed._sum.disbursedAmount || 0,
                totalCollected: totalCollected._sum.amount || 0,
                activeLoans,
                overdueLoans
            },
            recentActivities,
            period: {
                startDate,
                endDate
            }
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(result);
    } catch (error) {
        console.error('Error in getOverviewReport:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            overview: {
                totalLoans: 0,
                totalCustomers: 0,
                totalDisbursed: 0,
                totalCollected: 0,
                activeLoans: 0,
                overdueLoans: 0
            },
            recentActivities: [],
            period: {
                startDate,
                endDate
            },
            error: 'Failed to generate overview report'
        });
    }
}
async function getLoansReport(startDate, endDate) {
    try {
        console.log('Getting loans report for period:', startDate, 'to', endDate);
        const [loansByStatus, loansByType, disbursementTrend, averageLoanAmount] = await Promise.all([
            // Loans by status
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].loan.groupBy({
                by: [
                    'status'
                ],
                _count: {
                    id: true
                },
                _sum: {
                    principalAmount: true
                }
            }),
            // Loans by type
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].loan.groupBy({
                by: [
                    'loanTypeId'
                ],
                _count: {
                    id: true
                },
                _sum: {
                    principalAmount: true
                }
            }),
            // Disbursement trend (monthly) - show all disbursements, not just within date range
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$queryRaw`
      SELECT
        DATE_TRUNC('month', "disbursementDate") as month,
        COUNT(*) as count,
        SUM("disbursedAmount") as amount
      FROM loans
      WHERE "disbursementDate" IS NOT NULL
        AND "disbursedAmount" IS NOT NULL
      GROUP BY DATE_TRUNC('month', "disbursementDate")
      ORDER BY month DESC
      LIMIT 12
    `,
            // Average loan amount (all loans)
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].loan.aggregate({
                where: {
                    principalAmount: {
                        gt: 0
                    }
                },
                _avg: {
                    principalAmount: true
                }
            })
        ]);
        console.log('Loans report data:', {
            loansByStatusCount: loansByStatus.length,
            loansByTypeCount: loansByType.length,
            disbursementTrendCount: disbursementTrend.length,
            averageLoanAmount: averageLoanAmount._avg.principalAmount
        });
        // Convert all BigInt values to numbers for JSON serialization
        const result = convertBigIntToNumber({
            loansByStatus,
            loansByType,
            disbursementTrend,
            averageLoanAmount: averageLoanAmount._avg.principalAmount || 0,
            period: {
                startDate,
                endDate
            }
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(result);
    } catch (error) {
        console.error('Error in getLoansReport:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            loansByStatus: [],
            loansByType: [],
            disbursementTrend: [],
            averageLoanAmount: 0,
            period: {
                startDate,
                endDate
            },
            error: 'Failed to generate loans report'
        });
    }
}
async function getPaymentsReport(startDate, endDate) {
    try {
        console.log('Getting payments report for period:', startDate, 'to', endDate);
        const [paymentsByMethod, paymentTrend, totalCollections, averagePayment] = await Promise.all([
            // Payments by method (all payments)
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].payment.groupBy({
                by: [
                    'paymentMethod'
                ],
                _count: {
                    id: true
                },
                _sum: {
                    amount: true
                }
            }),
            // Payment trend (daily) - last 30 days or within date range
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$queryRaw`
      SELECT
        DATE_TRUNC('day', "paymentDate") as day,
        COUNT(*) as count,
        SUM("amount") as amount
      FROM payments
      WHERE "paymentDate" >= ${startDate}
        AND "paymentDate" <= ${endDate}
      GROUP BY DATE_TRUNC('day', "paymentDate")
      ORDER BY day DESC
    `,
            // Total collections (within date range)
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].payment.aggregate({
                where: {
                    paymentDate: {
                        gte: startDate,
                        lte: endDate
                    }
                },
                _sum: {
                    amount: true
                },
                _count: {
                    id: true
                }
            }),
            // Average payment (all payments)
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].payment.aggregate({
                _avg: {
                    amount: true
                }
            })
        ]);
        console.log('Payments report data:', {
            paymentsByMethodCount: paymentsByMethod.length,
            paymentTrendCount: paymentTrend.length,
            totalCollections: totalCollections._sum.amount,
            averagePayment: averagePayment._avg.amount
        });
        // Convert all BigInt values to numbers for JSON serialization
        const result = convertBigIntToNumber({
            paymentsByMethod,
            paymentTrend,
            totalCollections: {
                amount: totalCollections._sum.amount || 0,
                count: totalCollections._count.id || 0
            },
            averagePayment: averagePayment._avg.amount || 0,
            period: {
                startDate,
                endDate
            }
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(result);
    } catch (error) {
        console.error('Error in getPaymentsReport:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            paymentsByMethod: [],
            paymentTrend: [],
            totalCollections: {
                amount: 0,
                count: 0
            },
            averagePayment: 0,
            period: {
                startDate,
                endDate
            },
            error: 'Failed to generate payments report'
        });
    }
}
async function getCustomersReport(startDate, endDate) {
    try {
        console.log('Getting customers report for period:', startDate, 'to', endDate);
        const [newCustomers, customersByEmployment, customersByCity, averageIncome] = await Promise.all([
            // New customers
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].customer.count({
                where: {
                    createdAt: {
                        gte: startDate,
                        lte: endDate
                    }
                }
            }),
            // Customers by employment type
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].customer.groupBy({
                by: [
                    'employmentType'
                ],
                _count: {
                    id: true
                }
            }),
            // Customers by city
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].customer.groupBy({
                by: [
                    'city'
                ],
                _count: {
                    id: true
                }
            }),
            // Average monthly income
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].customer.aggregate({
                _avg: {
                    monthlyIncome: true
                }
            })
        ]);
        console.log('Customers report data:', {
            newCustomers,
            customersByEmploymentCount: customersByEmployment.length,
            customersByCityCount: customersByCity.length,
            averageIncome: averageIncome._avg.monthlyIncome
        });
        const result = {
            newCustomers,
            customersByEmployment,
            customersByCity,
            averageIncome: averageIncome._avg.monthlyIncome || 0,
            period: {
                startDate,
                endDate
            }
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(result);
    } catch (error) {
        console.error('Error in getCustomersReport:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            newCustomers: 0,
            customersByEmployment: [],
            customersByCity: [],
            averageIncome: 0,
            period: {
                startDate,
                endDate
            },
            error: 'Failed to generate customers report'
        });
    }
}
async function getPortfolioReport(startDate, endDate) {
    try {
        console.log('Getting portfolio report for period:', startDate, 'to', endDate);
        const [portfolioValue, outstandingAmount, collectionRate, riskAnalysis] = await Promise.all([
            // Total portfolio value
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].loan.aggregate({
                where: {
                    status: {
                        in: [
                            'ACTIVE',
                            'DISBURSED'
                        ]
                    }
                },
                _sum: {
                    totalAmount: true
                }
            }),
            // Outstanding amount
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].loan.aggregate({
                where: {
                    status: {
                        in: [
                            'ACTIVE',
                            'DISBURSED'
                        ]
                    }
                },
                _sum: {
                    disbursedAmount: true
                }
            }),
            // Collection rate (simplified)
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].payment.aggregate({
                where: {
                    paymentDate: {
                        gte: startDate,
                        lte: endDate
                    }
                },
                _sum: {
                    amount: true
                }
            }),
            // Risk analysis (loans by status)
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].loan.groupBy({
                by: [
                    'status'
                ],
                _count: {
                    id: true
                },
                _sum: {
                    disbursedAmount: true
                }
            })
        ]);
        console.log('Portfolio report data:', {
            portfolioValue: portfolioValue._sum.totalAmount,
            outstandingAmount: outstandingAmount._sum.disbursedAmount,
            collectionRate: collectionRate._sum.amount,
            riskAnalysisCount: riskAnalysis.length
        });
        const result = {
            portfolioValue: portfolioValue._sum.totalAmount || 0,
            outstandingAmount: outstandingAmount._sum.disbursedAmount || 0,
            collectionRate: collectionRate._sum.amount || 0,
            riskAnalysis,
            period: {
                startDate,
                endDate
            }
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(result);
    } catch (error) {
        console.error('Error in getPortfolioReport:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            portfolioValue: 0,
            outstandingAmount: 0,
            collectionRate: 0,
            riskAnalysis: [],
            period: {
                startDate,
                endDate
            },
            error: 'Failed to generate portfolio report'
        });
    }
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__e6eb4998._.js.map