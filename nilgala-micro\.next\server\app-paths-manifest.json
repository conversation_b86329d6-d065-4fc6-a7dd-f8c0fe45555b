{"/admin/backups/page": "app/admin/backups/page.js", "/api/admin/backups/route": "app/api/admin/backups/route.js", "/api/admin/permissions/route": "app/api/admin/permissions/route.js", "/api/admin/users/route": "app/api/admin/users/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/company-settings/route": "app/api/company-settings/route.js", "/api/customers/[id]/detailed/route": "app/api/customers/[id]/detailed/route.js", "/api/customers/route": "app/api/customers/route.js", "/api/dashboard/stats/route": "app/api/dashboard/stats/route.js", "/api/documents/[id]/view/route": "app/api/documents/[id]/view/route.js", "/api/documents/route": "app/api/documents/route.js", "/api/guarantor-documents/[id]/view/route": "app/api/guarantor-documents/[id]/view/route.js", "/api/loan-types/route": "app/api/loan-types/route.js", "/api/loans/[id]/activities/route": "app/api/loans/[id]/activities/route.js", "/api/loans/[id]/approve/route": "app/api/loans/[id]/approve/route.js", "/api/loans/[id]/comments/route": "app/api/loans/[id]/comments/route.js", "/api/loans/[id]/disburse/route": "app/api/loans/[id]/disburse/route.js", "/api/loans/[id]/route": "app/api/loans/[id]/route.js", "/api/loans/[id]/schedule/route": "app/api/loans/[id]/schedule/route.js", "/api/loans/pending/route": "app/api/loans/pending/route.js", "/api/loans/route": "app/api/loans/route.js", "/api/loans/stats/route": "app/api/loans/stats/route.js", "/api/performance/route": "app/api/performance/route.js", "/api/reports/route": "app/api/reports/route.js", "/api/required-documents/route": "app/api/required-documents/route.js", "/api/user/permissions/route": "app/api/user/permissions/route.js", "/auth/signin/page": "app/auth/signin/page.js", "/dashboard/page": "app/dashboard/page.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/loans/[id]/page": "app/loans/[id]/page.js", "/loans/new/page": "app/loans/new/page.js", "/loans/page": "app/loans/page.js", "/loans/pending/page": "app/loans/pending/page.js", "/page": "app/page.js", "/performance/page": "app/performance/page.js", "/reports/page": "app/reports/page.js"}