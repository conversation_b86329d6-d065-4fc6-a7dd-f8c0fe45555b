{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient()\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/lib/auth.ts"], "sourcesContent": ["import { NextAuthOptions } from 'next-auth'\nimport Cred<PERSON><PERSON><PERSON>rovider from 'next-auth/providers/credentials'\nimport { PrismaAdapter } from '@auth/prisma-adapter'\nimport { prisma } from './prisma'\nimport bcrypt from 'bcryptjs'\nimport { UserRole } from '@prisma/client'\n\n// Cache for role permissions to avoid database calls on every request\nlet rolePermissionsCache: Record<string, string[]> = {}\nlet cacheLastUpdated = 0\nconst CACHE_DURATION = 5 * 60 * 1000 // 5 minutes\n\nexport const authOptions: NextAuthOptions = {\n  adapter: PrismaAdapter(prisma) as any,\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null\n        }\n\n        const user = await prisma.user.findUnique({\n          where: {\n            email: credentials.email\n          }\n        })\n\n        if (!user || !user.isActive) {\n          return null\n        }\n\n        const isPasswordValid = await bcrypt.compare(\n          credentials.password,\n          user.password\n        )\n\n        if (!isPasswordValid) {\n          return null\n        }\n\n        // Update last login\n        await prisma.user.update({\n          where: { id: user.id },\n          data: { lastLogin: new Date() }\n        })\n\n        return {\n          id: user.id,\n          email: user.email,\n          firstName: user.firstName,\n          lastName: user.lastName,\n          role: user.role,\n          avatar: user.avatar || undefined,\n        }\n      }\n    })\n  ],\n  session: {\n    strategy: 'jwt'\n  },\n  callbacks: {\n    async jwt({ token, user }) {\n      if (user) {\n        token.role = user.role\n        token.firstName = user.firstName\n        token.lastName = user.lastName\n      }\n      return token\n    },\n    async session({ session, token }) {\n      if (token) {\n        session.user.id = token.sub!\n        session.user.role = token.role as UserRole\n        session.user.firstName = token.firstName as string\n        session.user.lastName = token.lastName as string\n      }\n      return session\n    }\n  },\n  pages: {\n    signIn: '/auth/signin',\n    error: '/auth/error',\n  }\n}\n\n// Load role permissions from database with caching\nasync function loadRolePermissions(): Promise<Record<string, string[]>> {\n  const now = Date.now()\n\n  // Return cached permissions if still valid\n  if (cacheLastUpdated && (now - cacheLastUpdated) < CACHE_DURATION) {\n    return rolePermissionsCache\n  }\n\n  try {\n    // Fetch all active role permissions from database\n    const permissions = await prisma.rolePermission.findMany({\n      where: { isActive: true },\n      select: { role: true, permission: true }\n    })\n\n    // Group permissions by role\n    const rolePermissions: Record<string, string[]> = {}\n    for (const perm of permissions) {\n      if (!rolePermissions[perm.role]) {\n        rolePermissions[perm.role] = []\n      }\n      rolePermissions[perm.role].push(perm.permission)\n    }\n\n    // Update cache\n    rolePermissionsCache = rolePermissions\n    cacheLastUpdated = now\n\n    return rolePermissions\n  } catch (error) {\n    console.error('Error loading role permissions from database:', error)\n    // Return empty permissions on error to be safe\n    return {}\n  }\n}\n\n// Get role permissions (cached)\nexport async function getRolePermissions(): Promise<Record<string, string[]>> {\n  return await loadRolePermissions()\n}\n\n// Clear permissions cache (call this when permissions are updated)\nexport function clearPermissionsCache() {\n  rolePermissionsCache = {}\n  cacheLastUpdated = 0\n}\n\n// Initialize permissions cache on server startup\nexport async function initializePermissionsCache() {\n  try {\n    await loadRolePermissions()\n    console.log('✅ Permissions cache initialized')\n  } catch (error) {\n    console.error('❌ Failed to initialize permissions cache:', error)\n  }\n}\n\nexport async function hasPermission(userRole: UserRole, permission: string): Promise<boolean> {\n  const rolePermissions = await getRolePermissions()\n  return rolePermissions[userRole]?.includes(permission) || false\n}\n\nexport async function checkPermissions(userRole: UserRole, requiredPermissions: string[]): Promise<boolean> {\n  const rolePermissions = await getRolePermissions()\n  return requiredPermissions.every(permission => rolePermissions[userRole]?.includes(permission) || false)\n}\n\n// Synchronous version for backward compatibility (uses cache)\nexport function hasPermissionSync(userRole: UserRole, permission: string): boolean {\n  return rolePermissionsCache[userRole]?.includes(permission) || false\n}\n\nexport function checkPermissionsSync(userRole: UserRole, requiredPermissions: string[]): boolean {\n  return requiredPermissions.every(permission => hasPermissionSync(userRole, permission))\n}\n"], "names": [], "mappings": ";;;;;;;;;;AACA;AACA;AACA;AACA;;;;;AAGA,sEAAsE;AACtE,IAAI,uBAAiD,CAAC;AACtD,IAAI,mBAAmB;AACvB,MAAM,iBAAiB,IAAI,KAAK,KAAK,YAAY;;AAE1C,MAAM,cAA+B;IAC1C,SAAS,CAAA,GAAA,sJAAA,CAAA,gBAAa,AAAD,EAAE,sHAAA,CAAA,SAAM;IAC7B,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;oBACxC,OAAO;wBACL,OAAO,YAAY,KAAK;oBAC1B;gBACF;gBAEA,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;oBAC3B,OAAO;gBACT;gBAEA,MAAM,kBAAkB,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAC1C,YAAY,QAAQ,EACpB,KAAK,QAAQ;gBAGf,IAAI,CAAC,iBAAiB;oBACpB,OAAO;gBACT;gBAEA,oBAAoB;gBACpB,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;oBACvB,OAAO;wBAAE,IAAI,KAAK,EAAE;oBAAC;oBACrB,MAAM;wBAAE,WAAW,IAAI;oBAAO;gBAChC;gBAEA,OAAO;oBACL,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,WAAW,KAAK,SAAS;oBACzB,UAAU,KAAK,QAAQ;oBACvB,MAAM,KAAK,IAAI;oBACf,QAAQ,KAAK,MAAM,IAAI;gBACzB;YACF;QACF;KACD;IACD,SAAS;QACP,UAAU;IACZ;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,SAAS,GAAG,KAAK,SAAS;gBAChC,MAAM,QAAQ,GAAG,KAAK,QAAQ;YAChC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,OAAO;gBACT,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;gBAC3B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,SAAS,GAAG,MAAM,SAAS;gBACxC,QAAQ,IAAI,CAAC,QAAQ,GAAG,MAAM,QAAQ;YACxC;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;AACF;AAEA,mDAAmD;AACnD,eAAe;IACb,MAAM,MAAM,KAAK,GAAG;IAEpB,2CAA2C;IAC3C,IAAI,oBAAoB,AAAC,MAAM,mBAAoB,gBAAgB;QACjE,OAAO;IACT;IAEA,IAAI;QACF,kDAAkD;QAClD,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,cAAc,CAAC,QAAQ,CAAC;YACvD,OAAO;gBAAE,UAAU;YAAK;YACxB,QAAQ;gBAAE,MAAM;gBAAM,YAAY;YAAK;QACzC;QAEA,4BAA4B;QAC5B,MAAM,kBAA4C,CAAC;QACnD,KAAK,MAAM,QAAQ,YAAa;YAC9B,IAAI,CAAC,eAAe,CAAC,KAAK,IAAI,CAAC,EAAE;gBAC/B,eAAe,CAAC,KAAK,IAAI,CAAC,GAAG,EAAE;YACjC;YACA,eAAe,CAAC,KAAK,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,UAAU;QACjD;QAEA,eAAe;QACf,uBAAuB;QACvB,mBAAmB;QAEnB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iDAAiD;QAC/D,+CAA+C;QAC/C,OAAO,CAAC;IACV;AACF;AAGO,eAAe;IACpB,OAAO,MAAM;AACf;AAGO,SAAS;IACd,uBAAuB,CAAC;IACxB,mBAAmB;AACrB;AAGO,eAAe;IACpB,IAAI;QACF,MAAM;QACN,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;IAC7D;AACF;AAEO,eAAe,cAAc,QAAkB,EAAE,UAAkB;IACxE,MAAM,kBAAkB,MAAM;IAC9B,OAAO,eAAe,CAAC,SAAS,EAAE,SAAS,eAAe;AAC5D;AAEO,eAAe,iBAAiB,QAAkB,EAAE,mBAA6B;IACtF,MAAM,kBAAkB,MAAM;IAC9B,OAAO,oBAAoB,KAAK,CAAC,CAAA,aAAc,eAAe,CAAC,SAAS,EAAE,SAAS,eAAe;AACpG;AAGO,SAAS,kBAAkB,QAAkB,EAAE,UAAkB;IACtE,OAAO,oBAAoB,CAAC,SAAS,EAAE,SAAS,eAAe;AACjE;AAEO,SAAS,qBAAqB,QAAkB,EAAE,mBAA6B;IACpF,OAAO,oBAAoB,KAAK,CAAC,CAAA,aAAc,kBAAkB,UAAU;AAC7E", "debugId": null}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/micro_finance/nilgala-micro/src/app/api/loans/%5Bid%5D/activities/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { getServerSession } from 'next-auth'\nimport { authOptions } from '@/lib/auth'\nimport { prisma } from '@/lib/prisma'\nimport { hasPermission } from '@/lib/auth'\n\n// GET /api/loans/[id]/activities - Get loan activity log\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const session = await getServerSession(authOptions)\n\n    if (!session || !hasPermission(session.user.role, 'loans:read')) {\n      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })\n    }\n\n    const { id } = await params\n\n    // Verify loan exists and user has access\n    const loan = await prisma.loan.findUnique({\n      where: { id }\n    })\n\n    if (!loan) {\n      return NextResponse.json({ error: 'Loan not found' }, { status: 404 })\n    }\n\n    // Get all audit logs related to this loan\n    const activities = await prisma.auditLog.findMany({\n      where: {\n        OR: [\n          // Direct loan activities\n          {\n            resource: 'Loan',\n            resourceId: id\n          },\n          // Payment activities for this loan\n          {\n            resource: 'Payment',\n            resourceId: id\n          },\n          // Document activities for this loan\n          {\n            resource: 'Document',\n            resourceId: id\n          },\n          // Guarantor activities for this loan\n          {\n            resource: 'Guarantor',\n            resourceId: id\n          },\n          // Payment schedule activities for this loan\n          {\n            resource: 'PaymentSchedule',\n            resourceId: id\n          }\n        ]\n      },\n      include: {\n        user: {\n          select: {\n            id: true,\n            firstName: true,\n            lastName: true,\n            role: true,\n            email: true\n          }\n        }\n      },\n      orderBy: {\n        timestamp: 'desc'\n      }\n    })\n\n    // Add some synthetic activities for better loan history\n    const syntheticActivities = []\n\n    // Add loan creation activity if not already in audit logs\n    const hasCreationLog = activities.some(a =>\n      a.action === 'CREATE' && a.resource === 'Loan'\n    )\n\n    if (!hasCreationLog) {\n      syntheticActivities.push({\n        id: `synthetic-create-${loan.id}`,\n        action: 'CREATE',\n        resource: 'Loan',\n        resourceId: loan.id,\n        details: `Loan application ${loan.loanNumber} created with principal amount ${loan.principalAmount}`,\n        timestamp: loan.createdAt,\n        user: {\n          id: loan.createdBy || 'system',\n          firstName: 'System',\n          lastName: 'User',\n          role: 'SYSTEM',\n          email: '<EMAIL>'\n        }\n      })\n    }\n\n    // Add disbursement activity if loan is disbursed\n    if (loan.status === 'DISBURSED' || loan.status === 'ACTIVE') {\n      const hasDisbursementLog = activities.some(a =>\n        a.newValues && JSON.stringify(a.newValues).toLowerCase().includes('disbursed')\n      )\n\n      if (!hasDisbursementLog && loan.disbursementDate) {\n        syntheticActivities.push({\n          id: `synthetic-disburse-${loan.id}`,\n          action: 'UPDATE',\n          resource: 'Loan',\n          resourceId: loan.id,\n          details: `Loan disbursed with amount ${loan.disbursedAmount || loan.principalAmount}`,\n          timestamp: loan.disbursementDate,\n          user: {\n            id: 'system',\n            firstName: 'System',\n            lastName: 'User',\n            role: 'SYSTEM',\n            email: '<EMAIL>'\n          }\n        })\n      }\n    }\n\n    // Combine real and synthetic activities\n    const allActivities = [\n      ...activities.map(activity => ({\n        id: activity.id,\n        action: activity.action,\n        resource: activity.resource,\n        resourceId: activity.resourceId,\n        details: activity.newValues ? JSON.stringify(activity.newValues) : activity.action,\n        timestamp: activity.timestamp.toISOString(),\n        user: activity.user\n      })),\n      ...syntheticActivities\n    ]\n\n    // Sort by timestamp (newest first)\n    allActivities.sort((a, b) =>\n      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()\n    )\n\n    return NextResponse.json(allActivities)\n\n  } catch (error) {\n    console.error('Error fetching loan activities:', error)\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;;AAIO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,oHAAA,CAAA,cAAW;QAElD,IAAI,CAAC,WAAW,CAAC,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,IAAI,CAAC,IAAI,EAAE,eAAe;YAC/D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAe,GAAG;gBAAE,QAAQ;YAAI;QACpE;QAEA,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QAErB,yCAAyC;QACzC,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,OAAO;gBAAE;YAAG;QACd;QAEA,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAiB,GAAG;gBAAE,QAAQ;YAAI;QACtE;QAEA,0CAA0C;QAC1C,MAAM,aAAa,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAChD,OAAO;gBACL,IAAI;oBACF,yBAAyB;oBACzB;wBACE,UAAU;wBACV,YAAY;oBACd;oBACA,mCAAmC;oBACnC;wBACE,UAAU;wBACV,YAAY;oBACd;oBACA,oCAAoC;oBACpC;wBACE,UAAU;wBACV,YAAY;oBACd;oBACA,qCAAqC;oBACrC;wBACE,UAAU;wBACV,YAAY;oBACd;oBACA,4CAA4C;oBAC5C;wBACE,UAAU;wBACV,YAAY;oBACd;iBACD;YACH;YACA,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,WAAW;wBACX,UAAU;wBACV,MAAM;wBACN,OAAO;oBACT;gBACF;YACF;YACA,SAAS;gBACP,WAAW;YACb;QACF;QAEA,wDAAwD;QACxD,MAAM,sBAAsB,EAAE;QAE9B,0DAA0D;QAC1D,MAAM,iBAAiB,WAAW,IAAI,CAAC,CAAA,IACrC,EAAE,MAAM,KAAK,YAAY,EAAE,QAAQ,KAAK;QAG1C,IAAI,CAAC,gBAAgB;YACnB,oBAAoB,IAAI,CAAC;gBACvB,IAAI,CAAC,iBAAiB,EAAE,KAAK,EAAE,EAAE;gBACjC,QAAQ;gBACR,UAAU;gBACV,YAAY,KAAK,EAAE;gBACnB,SAAS,CAAC,iBAAiB,EAAE,KAAK,UAAU,CAAC,+BAA+B,EAAE,KAAK,eAAe,EAAE;gBACpG,WAAW,KAAK,SAAS;gBACzB,MAAM;oBACJ,IAAI,KAAK,SAAS,IAAI;oBACtB,WAAW;oBACX,UAAU;oBACV,MAAM;oBACN,OAAO;gBACT;YACF;QACF;QAEA,iDAAiD;QACjD,IAAI,KAAK,MAAM,KAAK,eAAe,KAAK,MAAM,KAAK,UAAU;YAC3D,MAAM,qBAAqB,WAAW,IAAI,CAAC,CAAA,IACzC,EAAE,SAAS,IAAI,KAAK,SAAS,CAAC,EAAE,SAAS,EAAE,WAAW,GAAG,QAAQ,CAAC;YAGpE,IAAI,CAAC,sBAAsB,KAAK,gBAAgB,EAAE;gBAChD,oBAAoB,IAAI,CAAC;oBACvB,IAAI,CAAC,mBAAmB,EAAE,KAAK,EAAE,EAAE;oBACnC,QAAQ;oBACR,UAAU;oBACV,YAAY,KAAK,EAAE;oBACnB,SAAS,CAAC,2BAA2B,EAAE,KAAK,eAAe,IAAI,KAAK,eAAe,EAAE;oBACrF,WAAW,KAAK,gBAAgB;oBAChC,MAAM;wBACJ,IAAI;wBACJ,WAAW;wBACX,UAAU;wBACV,MAAM;wBACN,OAAO;oBACT;gBACF;YACF;QACF;QAEA,wCAAwC;QACxC,MAAM,gBAAgB;eACjB,WAAW,GAAG,CAAC,CAAA,WAAY,CAAC;oBAC7B,IAAI,SAAS,EAAE;oBACf,QAAQ,SAAS,MAAM;oBACvB,UAAU,SAAS,QAAQ;oBAC3B,YAAY,SAAS,UAAU;oBAC/B,SAAS,SAAS,SAAS,GAAG,KAAK,SAAS,CAAC,SAAS,SAAS,IAAI,SAAS,MAAM;oBAClF,WAAW,SAAS,SAAS,CAAC,WAAW;oBACzC,MAAM,SAAS,IAAI;gBACrB,CAAC;eACE;SACJ;QAED,mCAAmC;QACnC,cAAc,IAAI,CAAC,CAAC,GAAG,IACrB,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;QAGjE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAE3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}