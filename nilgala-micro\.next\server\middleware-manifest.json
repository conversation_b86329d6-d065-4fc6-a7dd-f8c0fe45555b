{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_b7197e5b._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_c615bfc1.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api\\/auth|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "q4fpFhy15lMFHqvmYhV0VixNY3/5We9bwvc5TD99qXU=", "__NEXT_PREVIEW_MODE_ID": "bcd33dd68200c640331f9e2ef218b248", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7914f20bb6783b1d120db33e8b78ba1907c29e1d9b512e02e6229b5ff208948f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "c6c840476159943d9d19e5f08fdd77ed24f785b211e33969e2e69ea590882507"}}}, "sortedMiddleware": ["/"], "functions": {}}